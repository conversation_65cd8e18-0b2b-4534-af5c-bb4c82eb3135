"""Overhaul management widget for the equipment inventory application."""
import logging
from datetime import datetime, date, timedelta

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QComboBox, QPushButton, QFileDialog, QFormLayout, QTextEdit, QDateEdit, QLabel, QFrame, QCompleter
from PyQt5.QtCore import Qt, QDate

import locale
locale.setlocale(locale.LC_ALL, '')

import database
import overhaul_service
import config
import utils
from models import Equipment, Repair, Overhaul, MediumReset
from ui.custom_widgets import ReadOnlyTableWidget, StatusLabel

# Configure logger
logger = logging.getLogger('overhaul_widget')

class OverhaulWidget(QWidget):
    """Widget for displaying overhaul table only (logic stripped as per user)."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_overhaul_id = None
        # Main layout with toolbar and table
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(12, 8, 12, 8)
        self.setLayout(main_layout)

        # ---- Toolbar ----
        toolbar = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Filter by BA, serial, equipment...")
        self.search_edit.textChanged.connect(self.apply_filters)

        self.status_combo = QComboBox()
        self.status_combo.addItems(["All", "Reminder", "Warning", "Overdue"])
        self.status_combo.currentIndexChanged.connect(self.apply_filters)

        clear_btn = QPushButton("Clear")
        clear_btn.clicked.connect(self.clear_filters)

        export_btn = QPushButton("Export CSV")
        export_btn.clicked.connect(self.export_csv)

        toolbar.addWidget(self.search_edit)
        toolbar.addWidget(self.status_combo)
        toolbar.addWidget(clear_btn)
        toolbar.addStretch()
        toolbar.addWidget(export_btn)
        main_layout.addLayout(toolbar)

        # ---- Table ----
        self.overhauls_table = ReadOnlyTableWidget()
        self.overhauls_table.setSortingEnabled(True)
        self.overhauls_table.setMinimumHeight(500)
        main_layout.addWidget(self.overhauls_table)

        # ---- Details Panel ----
        details_layout = QFormLayout()
        details_layout.setLabelAlignment(Qt.AlignRight)
        details_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignTop)
        self.detail_equipment = QLabel()
        self.detail_ba = QLabel()
        self.ba_input = QLineEdit()
        self.ba_input.setPlaceholderText("Enter BA Number")
        self.ba_input.hide()
        # completer
        ba_numbers = [str(e.get('ba_number')) for e in Equipment.get_all() if e.get('ba_number') not in [None,'','None']]
        self.ba_completer = QCompleter(ba_numbers)
        self.ba_completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.ba_input.setCompleter(self.ba_completer)
        self.ba_input.textChanged.connect(self.on_ba_changed)
        self.detail_meterage = QLabel()
        self.oh1_done = QDateEdit(calendarPopup=True)
        self.oh1_due = QDateEdit(calendarPopup=True)
        self.oh1_status_lbl = QLabel()
        self.oh2_done = QDateEdit(calendarPopup=True)
        self.oh2_due = QDateEdit(calendarPopup=True)
        self.oh2_status_lbl = QLabel()
        self.desc_edit = QTextEdit()
        self.desc_edit.setFixedHeight(60)

        details_layout.addRow("Equipment:", self.detail_equipment)
        details_layout.addRow("BA Number:", self.detail_ba)
        details_layout.addRow("", self.ba_input)
        details_layout.addRow("Meterage KM:", self.detail_meterage)
        details_layout.addRow("OH1 Done Date:", self.oh1_done)
        details_layout.addRow("OH1 Due Date:", self.oh1_due)
        details_layout.addRow("OH1 Status:", self.oh1_status_lbl)
        details_layout.addRow("OH2 Done Date:", self.oh2_done)
        details_layout.addRow("OH2 Due Date:", self.oh2_due)
        details_layout.addRow("OH2 Status:", self.oh2_status_lbl)
        details_layout.addRow("Description:", self.desc_edit)

        # Action buttons
        actions = QHBoxLayout()
        self.add_btn = QPushButton("Add New")
        self.save_btn = QPushButton("Save")
        self.delete_btn = QPushButton("Delete")

        # Completion buttons
        completion_actions = QHBoxLayout()
        self.complete_oh1_btn = QPushButton("Mark OH-I Complete")
        self.complete_oh1_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.complete_oh2_btn = QPushButton("Mark OH-II Complete")
        self.complete_oh2_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold;")

        completion_actions.addWidget(self.complete_oh1_btn)
        completion_actions.addWidget(self.complete_oh2_btn)
        completion_actions.addStretch()

        actions.addStretch()
        actions.addWidget(self.add_btn)
        actions.addWidget(self.save_btn)
        actions.addWidget(self.delete_btn)

        vbox_details = QVBoxLayout()
        vbox_details.addLayout(details_layout)
        vbox_details.addLayout(completion_actions)
        vbox_details.addLayout(actions)
        main_layout.addLayout(vbox_details)

        # Connections
        self.overhauls_table.itemSelectionChanged.connect(self.on_row_selected)
        self.add_btn.clicked.connect(self.on_add)
        self.save_btn.clicked.connect(self.on_save)
        self.delete_btn.clicked.connect(self.on_delete)
        self.complete_oh1_btn.clicked.connect(self.on_complete_oh1)
        self.complete_oh2_btn.clicked.connect(self.on_complete_oh2)

        # Initially disable completion buttons
        self.complete_oh1_btn.setEnabled(False)
        self.complete_oh2_btn.setEnabled(False)

        # Load data initially
        self.load_data()

    def load_data(self):
        """Load overhauls data from the Overhaul table with combined OH-I and OH-II per equipment."""
        logger.info("Loading overhauls data from Overhaul table")
        try:
            # Get all overhauls from the Overhaul table
            overhauls_list = Overhaul.get_all()

            # Build a mapping of equipment_id to equipment name and details
            equipment_map = {}
            equipment_details = {}
            equipment_ba_map = {}
            for eq in Equipment.get_all():
                # Use BA number priority formatting
                equipment_map[eq['equipment_id']] = utils.format_equipment_display(eq)
                equipment_details[eq['equipment_id']] = eq
                if eq.get('ba_number') not in [None, '', 'None']:
                    equipment_ba_map[eq['ba_number']] = eq

            # Group overhauls by equipment_id
            equipment_overhauls = {}
            for overhaul in overhauls_list:
                equipment_id = overhaul.get('equipment_id')
                if not equipment_id:
                    continue  # Skip records without equipment
                if equipment_id not in equipment_overhauls:
                    equipment_overhauls[equipment_id] = {'OH-I': None, 'OH-II': None}

                overhaul_type = overhaul.get('overhaul_type', '')
                if overhaul_type in ['OH-I', 'OH-II']:
                    equipment_overhauls[equipment_id][overhaul_type] = overhaul

            headers = ["Equipment", "BA Number", "Release Date", "Meterage KM", "OH 1 Done Date", "OH 1 Due Date", "OH 1 Status", "OH 2 Done Date", "OH 2 Due Date", "OH 2 Status", "ID"]
            # Prepare data for table
            data = []

            for equipment_id, overhauls in equipment_overhauls.items():
                oh1 = overhauls.get('OH-I')
                oh2 = overhauls.get('OH-II')

                # Calculate OH-1 due date if missing using release date (+15 yrs)
                oh1_due_calculated = None
                if not (oh1 and oh1.get('due_date')) and equipment_details.get(equipment_id, {}).get('date_of_commission'):
                    try:
                        from datetime import datetime, timedelta
                        rel_date = equipment_details.get(equipment_id, {}).get('date_of_commission')
                        if isinstance(rel_date, str):
                            rel_date = datetime.strptime(rel_date.split(' ')[0], '%Y-%m-%d').date()
                        oh1_due_calculated = rel_date + timedelta(days=365*15)
                    except Exception:
                        pass
                raw_oh1_due = oh1.get('due_date') if oh1 else None
                oh1_due_str = self.format_date(raw_oh1_due if raw_oh1_due else oh1_due_calculated)
                oh1_done_str = self.format_date(oh1.get('done_date') if oh1 else None)

                # Calculate OH-2 due date (OH1 done +10 yrs)
                oh2_due_calculated = None
                if oh1 and oh1.get('done_date') and oh1.get('done_date') not in ['None', ""] and oh1.get('done_date') not in [None, ""]:
                    try:
                        from datetime import datetime, timedelta
                        oh1_done_date = datetime.strptime(oh1.get('done_date').split(' ')[0], '%Y-%m-%d').date()
                        oh2_due_calculated = oh1_done_date + timedelta(days=365*10)
                    except (ValueError, TypeError):
                        pass

                if oh2:
                    raw_oh2_due = oh2.get('due_date')
                    oh2_due_str = self.format_date(raw_oh2_due if raw_oh2_due else oh2_due_calculated)
                    oh2_done_str = self.format_date(oh2.get('done_date'))
                else:
                    oh2_due_str = self.format_date(oh2_due_calculated)
                    oh2_done_str = ""

                # Calculate discard due date only if OH-2 is completed (kept for internal logic but not displayed)
                # Prepare release date and km run values
                equip_info = equipment_details.get(equipment_id, {})
                # Prefer lookup by BA number to ensure correct matching
                ba_no = equip_info.get('ba_number')
                if ba_no and ba_no in equipment_ba_map:
                    equip_info = equipment_ba_map[ba_no] or equip_info

                release_date_val = equip_info.get('date_of_commission')
                release_date_str = self.format_date(release_date_val)

                # Meterage KM directly: prefer overhaul meter_reading, fallback to equipment table
                km_run_val = None
                if oh1 and oh1.get('meter_reading') not in [None, 0, '']:
                    km_run_val = oh1.get('meter_reading')
                elif oh2 and oh2.get('meter_reading') not in [None, 0, '']:
                    km_run_val = oh2.get('meter_reading')
                if km_run_val in [None, '', 0]:
                    km_run_val = equip_info.get('MeterageKMs') or equip_info.get('meterage_kms') or ''

                # Format meterage nicely
                meterage_str = self.format_kilometers(km_run_val)

                # Individual statuses for OH-1 and OH-2
                oh1_status = ''
                if oh1:
                    raw_status = oh1.get('status')
                    if raw_status not in [None, '', 'None', 'No']:
                        oh1_status = raw_status
                    else:
                        oh1_status = overhaul_service.get_overhaul_status(
                            'OH-I',
                            raw_oh1_due if raw_oh1_due else oh1_due_calculated,
                            oh1.get('done_date'),
                            date_of_commission=release_date_val,
                            meterage_km=km_run_val
                        )
                else:
                    # No OH1 record: derive status directly from release date logic
                    oh1_status = overhaul_service.get_overhaul_status(
                        'OH-I',
                        oh1_due_calculated,
                        None,
                        date_of_commission=release_date_val,
                        meterage_km=km_run_val
                    )

                oh2_status = ''
                if oh2:
                    raw_status2 = oh2.get('status')
                    if raw_status2 not in [None, '', 'None', 'No']:
                        oh2_status = raw_status2
                    else:
                        oh2_status = overhaul_service.get_overhaul_status(
                            'OH-II',
                            raw_oh2_due if raw_oh2_due else oh2_due_calculated,
                            oh2.get('done_date'),
                            date_of_commission=release_date_val,
                            oh1_done_date=oh1.get('done_date') if oh1 else None,
                            meterage_km=km_run_val
                        )
                else:
                    oh2_status = overhaul_service.get_overhaul_status(
                        'OH-II',
                        oh2_due_calculated,
                        None,
                        date_of_commission=release_date_val,
                        oh1_done_date=oh1.get('done_date') if oh1 else None,
                        meterage_km=km_run_val
                    )

                equipment_name = equipment_map.get(equipment_id, str(equipment_id))

                # Use the first available overhaul ID for row identification
                row_id = (oh1.get('overhaul_id') if oh1 else oh2.get('overhaul_id')) if (oh1 or oh2) else equipment_id

                row_data = {
                    "Equipment": equipment_name,
                    "BA Number": equip_info.get('ba_number', ''),
                    "Release Date": release_date_str,
                    "Meterage KM": meterage_str,
                    "OH 1 Done Date": oh1_done_str,
                    "OH 1 Due Date": oh1_due_str,
                    "OH 1 Status": oh1_status,
                    "OH 2 Done Date": oh2_done_str,
                    "OH 2 Due Date": oh2_due_str,
                    "OH 2 Status": oh2_status,
                    "ID": row_id
                }
                data.append(row_data)

            self.all_data = data  # store for filtering/export
            self.refresh_table()
            logger.info(f"Prepared {len(data)} overhaul rows")
        except Exception as e:
            logger.error(f"Error loading overhauls data: {e}")

    def apply_filters(self):
        """Filter self.all_data based on search text and status combo."""
        if not hasattr(self, 'all_data'):
            return
        text = self.search_edit.text().lower().strip()
        status_filter = self.status_combo.currentText()

        filtered = []
        for row in self.all_data:
            if status_filter != "All" and status_filter.lower() != row.get("OH 1 Status", "").lower() and status_filter.lower() != row.get("OH 2 Status", "").lower():
                continue
            if text and text not in str(row["Equipment"]).lower() and text not in str(row["BA Number"]).lower():
                continue
            filtered.append(row)
        self.overhauls_table.set_data(self.visible_headers(), filtered, id_column=len(self.visible_headers())-1)

    def clear_filters(self):
        self.search_edit.clear()
        self.status_combo.setCurrentIndex(0)

    def refresh_table(self):
        """Initial table set or after data reload."""
        self.apply_filters()

    def visible_headers(self):
        return ["Equipment", "BA Number", "Release Date", "Meterage KM", "OH 1 Done Date", "OH 1 Due Date", "OH 1 Status", "OH 2 Done Date", "OH 2 Due Date", "OH 2 Status", "ID"]

    def export_csv(self):
        if not hasattr(self, 'all_data') or not self.all_data:
            return
        path, _ = QFileDialog.getSaveFileName(self, "Export Overhaul Data", "overhaul.csv", "CSV Files (*.csv)")
        if not path:
            return
        import csv
        try:
            with open(path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=self.visible_headers()[:-1])  # exclude ID
                writer.writeheader()
                for row in self.all_data:
                    writer.writerow({k: row.get(k, '') for k in self.visible_headers()[:-1]})
        except Exception as e:
            logger.error(f"Export failed: {e}")

    def format_date(self, value):
        """Convert date or string value to DD/MM/YYYY string via utils."""
        return utils.format_date_for_display(value)

    def format_kilometers(self, value):
        """Return kilometer value with commas and no decimals, or '-' if not valid."""
        try:
            if value in [None, '', 'None']:
                return '-'
            val = float(value)
            if val <= 0:
                return '-'
            return f"{val:,.0f}"
        except (ValueError, TypeError):
            return str(value)

    # ---------- details panel handlers ----------
    def on_row_selected(self):
        selected = self.overhauls_table.selectedItems()
        if not selected:
            return
        row = selected[0].row()
        row_id_item = self.overhauls_table.item(row, self.overhauls_table.columnCount()-1)
        if not row_id_item:
            return
        self.current_overhaul_id = int(row_id_item.text())
        # find data dict
        data_dict = next((d for d in self.all_data if d['ID']==self.current_overhaul_id), None)
        if not data_dict:
            return
        self.populate_details(data_dict)

    def populate_details(self, d):
        self.detail_equipment.setText(d.get('Equipment',''))
        self.detail_ba.setText(d.get('BA Number',''))
        # Prefer meter_reading from the specific overhaul record if available, otherwise fall back to equipment meterage
        from models import Overhaul
        km_val = None
        try:
            if self.current_overhaul_id:
                oh_rec = Overhaul.get_by_id(self.current_overhaul_id)
                if oh_rec and oh_rec.get('meter_reading') not in [None, 0, '']:
                    km_val = oh_rec.get('meter_reading')
        except Exception:
            pass

        if km_val in [None, '', 0]:
            equip = next((e for e in Equipment.get_all() if str(e.get('ba_number'))==d.get('BA Number')), None)
            km_val = equip.get('MeterageKMs') or equip.get('meterage_kms') if equip else ''

            # Update completion buttons for this equipment
            if equip:
                self.update_completion_buttons(equip['equipment_id'])

        self.detail_meterage.setText(self.format_kilometers(km_val))
        self.set_qdate(self.oh1_done, d.get('OH 1 Done Date'))
        self.set_qdate(self.oh1_due, d.get('OH 1 Due Date'))
        self.oh1_status_lbl.setText(d.get('OH 1 Status',''))
        self.set_qdate(self.oh2_done, d.get('OH 2 Done Date'))
        self.set_qdate(self.oh2_due, d.get('OH 2 Due Date'))
        self.oh2_status_lbl.setText(d.get('OH 2 Status',''))
        self.desc_edit.setPlainText(d.get('description',''))

    def on_add(self):
        # clear form for new entry
        self.current_overhaul_id = None
        self.detail_equipment.setText('')
        self.detail_ba.setText('')
        self.detail_meterage.setText('')
        for w in [self.oh1_done, self.oh1_due, self.oh2_done, self.oh2_due]:
            w.setDate(QDate.currentDate())
        self.oh1_status_lbl.setText('')
        self.oh2_status_lbl.setText('')
        self.desc_edit.clear()
        self.ba_input.clear()
        self.ba_input.show()
        self.detail_ba.hide()

    def on_save(self):
        """Save overhaul data with enhanced validation and error handling."""
        try:
            from PyQt5.QtWidgets import QMessageBox

            # Get equipment BA number
            equip_ba = self.ba_input.text().strip() if self.current_overhaul_id is None else self.detail_ba.text().strip()

            if not equip_ba:
                QMessageBox.warning(self, "Validation Error", "Please enter a BA Number.")
                return

            # Find equipment
            equipment = next((e for e in Equipment.get_all() if str(e.get('ba_number'))==equip_ba), None)
            if not equipment:
                QMessageBox.warning(self, "Equipment Not Found", f"No equipment found with BA Number: {equip_ba}")
                return

            eq_id = equipment['equipment_id']

            # Validate dates
            oh1_done_str = self.qdate_to_str(self.oh1_done.date()) if self.oh1_done.date() != self.oh1_done.minimumDate() else None
            oh1_due_str = self.qdate_to_str(self.oh1_due.date())
            oh2_done_str = self.qdate_to_str(self.oh2_done.date()) if self.oh2_done.date() != self.oh2_done.minimumDate() else None
            oh2_due_str = self.qdate_to_str(self.oh2_due.date())

            # Validate OH-I logic
            if oh1_done_str and oh1_due_str:
                from datetime import datetime
                oh1_done_date = datetime.strptime(oh1_done_str, '%Y-%m-%d').date()
                oh1_due_date = datetime.strptime(oh1_due_str, '%Y-%m-%d').date()
                if oh1_done_date > oh1_due_date + timedelta(days=365):  # Allow 1 year grace period
                    reply = QMessageBox.question(self, "Date Validation",
                                               f"OH-I was completed significantly after due date.\n"
                                               f"Due: {oh1_due_str}\nDone: {oh1_done_str}\n"
                                               f"Continue saving?",
                                               QMessageBox.Yes | QMessageBox.No)
                    if reply == QMessageBox.No:
                        return

            # Calculate statuses
            meterage = equipment.get('MeterageKMs') or equipment.get('meterage_kms')
            oh1_status_val = overhaul_service.get_overhaul_status(
                'OH-I', oh1_due_str, oh1_done_str,
                date_of_commission=equipment.get('date_of_commission'),
                meterage_km=meterage
            )
            oh2_status_val = overhaul_service.get_overhaul_status(
                'OH-II', oh2_due_str, oh2_done_str,
                date_of_commission=equipment.get('date_of_commission'),
                oh1_done_date=oh1_done_str,
                meterage_km=meterage
            )

            # Check if overhauls already exist for this equipment
            existing_overhauls = Overhaul.get_by_equipment(eq_id)
            oh1_existing = next((oh for oh in existing_overhauls if oh.get('overhaul_type') == 'OH-I'), None)
            oh2_existing = next((oh for oh in existing_overhauls if oh.get('overhaul_type') == 'OH-II'), None)

            # Save or update OH-I
            if oh1_existing:
                Overhaul.update(oh1_existing['overhaul_id'],
                              done_date=oh1_done_str,
                              due_date=oh1_due_str,
                              status=oh1_status_val,
                              description=self.desc_edit.toPlainText(),
                              meter_reading=meterage)
            else:
                oh1 = Overhaul(
                    equipment_id=eq_id,
                    overhaul_type='OH-I',
                    done_date=oh1_done_str,
                    due_date=oh1_due_str,
                    status=oh1_status_val,
                    description=self.desc_edit.toPlainText(),
                    meter_reading=meterage
                )
                oh1.save()

            # Save or update OH-II
            if oh2_existing:
                Overhaul.update(oh2_existing['overhaul_id'],
                              done_date=oh2_done_str,
                              due_date=oh2_due_str,
                              status=oh2_status_val,
                              description=self.desc_edit.toPlainText(),
                              meter_reading=meterage)
            else:
                oh2 = Overhaul(
                    equipment_id=eq_id,
                    overhaul_type='OH-II',
                    done_date=oh2_done_str,
                    due_date=oh2_due_str,
                    status=oh2_status_val,
                    description=self.desc_edit.toPlainText(),
                    meter_reading=meterage
                )
                oh2.save()

            # Refresh data and UI
            self.load_data()
            self.detail_ba.setText(equip_ba)
            self.ba_input.hide()
            self.detail_ba.show()
            self.detail_meterage.setText(self.format_kilometers(meterage))

            QMessageBox.information(self, "Success", "Overhaul data saved successfully.")

        except Exception as e:
            logger.error(f'Save failed: {e}')
            QMessageBox.critical(self, "Save Error", f"Failed to save overhaul data:\n{str(e)}")

    def on_delete(self):
        if not self.current_overhaul_id:
            return
        try:
            Overhaul.delete_by_id(self.current_overhaul_id)
            self.load_data()
        except Exception as e:
            logger.error(f'Delete failed: {e}')

    # ----- local date helpers -----
    def set_qdate(self, widget, date_str):
        """Set QDateEdit widget from a DD/MM/YYYY or YYYY-MM-DD string."""
        if not date_str:
            widget.setDate(QDate.currentDate())
            return
        try:
            if isinstance(date_str, str):
                for fmt in ('%d/%m/%Y', '%Y-%m-%d', '%d-%m-%Y'):
                    try:
                        dt = datetime.strptime(date_str.split(' ')[0], fmt)
                        widget.setDate(QDate(dt.year, dt.month, dt.day))
                        return
                    except ValueError:
                        continue
        except Exception:
            pass
        widget.setDate(QDate.currentDate())

    def qdate_to_str(self, qdate):
        return qdate.toString('yyyy-MM-dd')

    def on_ba_changed(self, text):
        equip = next((e for e in Equipment.get_all() if str(e.get('ba_number'))==text), None)
        if equip:
            self.detail_equipment.setText(utils.format_equipment_display(equip))
            km_val = equip.get('MeterageKMs') or equip.get('meterage_kms')
            self.detail_meterage.setText(self.format_kilometers(km_val))
            self.update_completion_buttons(equip['equipment_id'])
        else:
            self.detail_equipment.setText('')
            self.detail_meterage.setText('')
            self.complete_oh1_btn.setEnabled(False)
            self.complete_oh2_btn.setEnabled(False)

    def update_completion_buttons(self, equipment_id):
        """Update completion button states based on equipment overhaul status."""
        try:
            import overhaul_service
            summary = overhaul_service.get_overhaul_summary(equipment_id)

            if summary:
                # OH-I completion button
                oh1_completed = summary['oh1']['done_date'] and summary['oh1']['done_date'] not in [None, '', 'None']
                self.complete_oh1_btn.setEnabled(not oh1_completed)
                self.complete_oh1_btn.setText("OH-I Completed" if oh1_completed else "Mark OH-I Complete")

                # OH-II completion button
                oh2_completed = summary['oh2']['done_date'] and summary['oh2']['done_date'] not in [None, '', 'None']
                can_complete_oh2 = summary['oh2']['can_complete'] and not oh2_completed
                self.complete_oh2_btn.setEnabled(can_complete_oh2)

                if oh2_completed:
                    self.complete_oh2_btn.setText("OH-II Completed")
                elif not summary['oh2']['can_complete']:
                    self.complete_oh2_btn.setText("Complete OH-I First")
                else:
                    self.complete_oh2_btn.setText("Mark OH-II Complete")
            else:
                self.complete_oh1_btn.setEnabled(False)
                self.complete_oh2_btn.setEnabled(False)

        except Exception as e:
            logger.error(f"Error updating completion buttons: {e}")
            self.complete_oh1_btn.setEnabled(False)
            self.complete_oh2_btn.setEnabled(False)

    def on_complete_oh1(self):
        """Handle OH-I completion."""
        self.complete_overhaul('OH-I')

    def on_complete_oh2(self):
        """Handle OH-II completion."""
        self.complete_overhaul('OH-II')

    def complete_overhaul(self, overhaul_type):
        """Complete an overhaul with date picker and validation."""
        try:
            from PyQt5.QtWidgets import QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit, QLineEdit
            from PyQt5.QtCore import QDate

            # Get current equipment
            equip_ba = self.detail_ba.text().strip()
            if not equip_ba:
                QMessageBox.warning(self, "No Equipment Selected", "Please select equipment first.")
                return

            equipment = next((e for e in Equipment.get_all() if str(e.get('ba_number'))==equip_ba), None)
            if not equipment:
                QMessageBox.warning(self, "Equipment Not Found", f"Equipment with BA {equip_ba} not found.")
                return

            # Create completion dialog
            dialog = QDialog(self)
            dialog.setWindowTitle(f"Complete {overhaul_type}")
            dialog.setModal(True)
            dialog.resize(400, 300)

            layout = QVBoxLayout(dialog)

            # Equipment info
            layout.addWidget(QLabel(f"Equipment: {utils.format_equipment_display(equipment)}"))
            layout.addWidget(QLabel(f"Overhaul Type: {overhaul_type}"))

            # Completion date
            layout.addWidget(QLabel("Completion Date:"))
            completion_date = QDateEdit(calendarPopup=True)
            completion_date.setDate(QDate.currentDate())
            completion_date.setMaximumDate(QDate.currentDate())  # Cannot be in future
            layout.addWidget(completion_date)

            # Completed by
            layout.addWidget(QLabel("Completed By:"))
            completed_by = QLineEdit()
            completed_by.setPlaceholderText("Enter name of person who completed the overhaul")
            layout.addWidget(completed_by)

            # Completion notes
            layout.addWidget(QLabel("Completion Notes:"))
            completion_notes = QTextEdit()
            completion_notes.setPlaceholderText("Enter any notes about the overhaul completion...")
            completion_notes.setMaximumHeight(80)
            layout.addWidget(completion_notes)

            # Meter reading
            layout.addWidget(QLabel("Meter Reading (KM):"))
            meter_reading = QLineEdit()
            current_km = equipment.get('MeterageKMs') or equipment.get('meterage_kms') or 0
            meter_reading.setText(str(current_km))
            layout.addWidget(meter_reading)

            # Buttons
            button_layout = QHBoxLayout()
            complete_btn = QPushButton("Complete Overhaul")
            complete_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
            cancel_btn = QPushButton("Cancel")

            button_layout.addWidget(complete_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            # Connect buttons
            complete_btn.clicked.connect(dialog.accept)
            cancel_btn.clicked.connect(dialog.reject)

            # Show dialog
            if dialog.exec_() == QDialog.Accepted:
                try:
                    import overhaul_service

                    # Get values
                    comp_date = completion_date.date().toPython()
                    comp_by = completed_by.text().strip()
                    comp_notes = completion_notes.toPlainText().strip()
                    meter_val = None

                    try:
                        meter_val = float(meter_reading.text().strip()) if meter_reading.text().strip() else None
                    except ValueError:
                        pass

                    # Complete the overhaul
                    success = overhaul_service.complete_overhaul(
                        equipment['equipment_id'],
                        overhaul_type,
                        comp_date,
                        comp_by if comp_by else None,
                        comp_notes if comp_notes else None,
                        meter_val
                    )

                    if success:
                        QMessageBox.information(self, "Success",
                                              f"{overhaul_type} completed successfully!\n\n"
                                              f"Completion Date: {comp_date}\n"
                                              f"Completed By: {comp_by or 'Not specified'}")

                        # Refresh data and UI
                        self.load_data()
                        self.update_completion_buttons(equipment['equipment_id'])

                        # Refresh dependent tabs
                        from PyQt5.QtCore import QTimer
                        QTimer.singleShot(200, self.refresh_dependent_tabs)

                except Exception as e:
                    QMessageBox.critical(self, "Completion Error",
                                       f"Error completing {overhaul_type}:\n\n{str(e)}")

        except Exception as e:
            logger.error(f"Error in complete_overhaul: {e}")
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")

    def refresh_dependent_tabs(self):
        """Refresh dependent tabs after overhaul completion."""
        try:
            # Get parent window and refresh other tabs
            parent = self.parent()
            while parent and not hasattr(parent, 'dashboard_widget'):
                parent = parent.parent()

            if parent:
                # Refresh dashboard
                if hasattr(parent, 'dashboard_widget'):
                    parent.dashboard_widget.load_data()

                # Refresh discard criteria tab
                if hasattr(parent, 'discard_criteria_widget'):
                    parent.discard_criteria_widget.load_data()

        except Exception as e:
            logger.error(f"Error refreshing dependent tabs: {e}")