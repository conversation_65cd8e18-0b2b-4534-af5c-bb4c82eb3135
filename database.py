"""Database initialization and connection management."""
import os
import logging
import sqlite3
import threading
from pathlib import Path
import time

# Configure logger
logger = logging.getLogger('database')

# Dedicated DB debug log file
from config import DB_PATH
DB_DEBUG_LOG = DB_PATH.replace("inventory.db", "db_debug.log")
def db_debug_log(msg):
    # Only log errors and important events, not routine operations
    important_keywords = ['error', 'failed', 'corruption', 'migration', 'backup']
    if any(keyword in msg.lower() for keyword in important_keywords):
        with open(DB_DEBUG_LOG, "a") as f:
            f.write(f"[DB_DEBUG] {msg}\n")

# Global connection data
from config import DB_PATH


def dict_factory(cursor, row):
    """Convert row results to dictionary for compatibility with existing code."""
    d = {}
    for idx, col in enumerate(cursor.description):
        d[col[0]] = row[idx]
    return d


import traceback

def handle_db_error(func_name, error):
    """Centralized database error handling with user-friendly messages."""
    if isinstance(error, sqlite3.IntegrityError):
        user_msg = "Data integrity constraint violated. Please check your input and try again."
        logger.error(f"{func_name}: {user_msg} - {str(error)}")
        return user_msg
    elif isinstance(error, sqlite3.OperationalError):
        user_msg = "Database operation failed. Please try again later."
        logger.error(f"{func_name}: {user_msg} - {str(error)}")
        return user_msg
    elif isinstance(error, sqlite3.DatabaseError):
        user_msg = "Database error occurred. Please contact support if this persists."
        logger.error(f"{func_name}: {user_msg} - {str(error)}")
        return user_msg
    else:
        user_msg = f"An unexpected error occurred: {str(error)}"
        logger.error(f"{func_name}: {user_msg}")
        return user_msg

def get_db_connection():
    """Create and return a new SQLite connection (caller must close). Ensures schema is up-to-date."""
    try:
        # Ensure parent directory exists
        db_dir = Path(DB_PATH).parent
        db_dir.mkdir(parents=True, exist_ok=True)

        db_debug_log(f"Opening SQLite connection to {DB_PATH}")
        conn = sqlite3.connect(DB_PATH, check_same_thread=False)
        conn.row_factory = dict_factory
        conn.execute("PRAGMA foreign_keys = ON")
        # Always check and update schema on every connection
        from database import check_and_update_schema
        logger.info("[DB MIGRATION] Checking and updating database schema on new connection...")
        db_debug_log("[DB MIGRATION] Checking and updating database schema on new connection...")
        migration_result = check_and_update_schema(conn)
        if migration_result:
            logger.info("[DB MIGRATION] Schema migration completed successfully on this connection.")
            db_debug_log("[DB MIGRATION] Schema migration completed successfully on this connection.")
        else:
            logger.error("[DB MIGRATION] Database schema migration failed. Some features may not work correctly.")
            db_debug_log("[DB MIGRATION] Database schema migration failed. Some features may not work correctly.")
        return conn
    except sqlite3.DatabaseError as db_err:
        # Handle corruption: backup and recreate
        logger.error(f"Database file may be corrupted: {db_err}\n{traceback.format_exc()}")
        db_debug_log(f"Database file may be corrupted: {db_err}\n{traceback.format_exc()}")
        try:
            if os.path.exists(DB_PATH):
                corrupt_path = DB_PATH + ".corrupt.bak"
                os.rename(DB_PATH, corrupt_path)
                logger.error(f"Backed up corrupt DB to: {corrupt_path}")
                db_debug_log(f"Backed up corrupt DB to: {corrupt_path}")
            # Try to create new DB
            conn = sqlite3.connect(DB_PATH, check_same_thread=False)
            conn.row_factory = dict_factory
            conn.execute("PRAGMA foreign_keys = ON")
            from database import check_and_update_schema
            check_and_update_schema(conn)
            logger.info("Created new blank database after corruption.")
            db_debug_log("Created new blank database after corruption.")
            return conn
        except Exception as e2:
            logger.error(f"Failed to recover from DB corruption: {e2}\n{traceback.format_exc()}")
            db_debug_log(f"Failed to recover from DB corruption: {e2}\n{traceback.format_exc()}")
            return None
    except Exception as e:
        logger.error(f"Error creating SQLite connection: {e}\n{traceback.format_exc()}")
        db_debug_log(f"Error creating SQLite connection: {e}\n{traceback.format_exc()}")
        return None


def close_db_connection(connection):
    """Close the SQLite connection."""
    if connection:
        connection.close()
        logger.debug("Closed SQLite connection")
        db_debug_log("Closed SQLite connection")


def init_connection_pool():
    """Initialize the database connection (no pool needed for SQLite). Returns (success, first_run)."""
    logger.info("Initializing SQLite database")
    try:
        db_dir = Path(DB_PATH).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        first_run = not os.path.exists(DB_PATH)
        connection = get_db_connection()
        if connection:
            logger.info("SQLite database initialized successfully")
            close_db_connection(connection)
            return True, first_run
        else:
            logger.error("Failed to connect to SQLite database")
            return False, first_run
    except Exception as e:
        import traceback
        logger.error(f"Error initializing SQLite database: {e}\n{traceback.format_exc()}")
        return False, False


def check_and_update_schema(connection):
    """Check and update database schema to ensure all required columns exist."""
    import traceback
    try:
        cursor = connection.cursor()
        db_debug_log("Checking schema for equipment table...")
        # Check if ba_number and hours_run_total columns exist in equipment table
        cursor.execute("PRAGMA table_info(equipment)")
        columns = cursor.fetchall()
        column_names = [col['name'] for col in columns]
        db_debug_log(f"Current equipment columns: {column_names}")
        # --- Robust, idempotent schema migration for ALL tables ---
        # Table: equipment (full migration)
        equipment_required_columns = [
            ('serial_number', "ALTER TABLE equipment ADD COLUMN serial_number TEXT"),
            ('make_and_type', "ALTER TABLE equipment ADD COLUMN make_and_type TEXT NOT NULL"),
            ('units_held', "ALTER TABLE equipment ADD COLUMN units_held INTEGER DEFAULT 1"),
            ('vintage_years', "ALTER TABLE equipment ADD COLUMN vintage_years REAL DEFAULT 0"),
            ('meterage_kms', "ALTER TABLE equipment ADD COLUMN meterage_kms REAL DEFAULT 0"),
            ('meterage_description', "ALTER TABLE equipment ADD COLUMN meterage_description TEXT"),
            ('km_hrs_run_previous_month', "ALTER TABLE equipment ADD COLUMN km_hrs_run_previous_month REAL DEFAULT 0"),
            ('km_hrs_run_current_month', "ALTER TABLE equipment ADD COLUMN km_hrs_run_current_month REAL DEFAULT 0"),
            ('is_active', "ALTER TABLE equipment ADD COLUMN is_active INTEGER DEFAULT 1"),
            ('remarks', "ALTER TABLE equipment ADD COLUMN remarks TEXT"),
            ('section', "ALTER TABLE equipment ADD COLUMN section TEXT"),
            ('unit', "ALTER TABLE equipment ADD COLUMN unit TEXT"),
            ('location', "ALTER TABLE equipment ADD COLUMN location TEXT"),
            ('last_service_date', "ALTER TABLE equipment ADD COLUMN last_service_date TEXT"),
            ('next_service_date', "ALTER TABLE equipment ADD COLUMN next_service_date TEXT"),
            ('status', "ALTER TABLE equipment ADD COLUMN status TEXT"),
            ('ba_number', "ALTER TABLE equipment ADD COLUMN ba_number TEXT"),
            ('hours_run_total', "ALTER TABLE equipment ADD COLUMN hours_run_total REAL DEFAULT 0"),
            ('hours_run_previous_month', "ALTER TABLE equipment ADD COLUMN hours_run_previous_month REAL DEFAULT 0"),
            ('hours_run_current_month', "ALTER TABLE equipment ADD COLUMN hours_run_current_month REAL DEFAULT 0"),
            ('date_of_induction', "ALTER TABLE equipment ADD COLUMN date_of_induction TEXT"),
            ('date_of_commission', "ALTER TABLE equipment ADD COLUMN date_of_commission TEXT")
        ]
        cursor.execute("PRAGMA table_info(equipment)")
        eq_columns = [col['name'] for col in cursor.fetchall()]
        for col_name, alter_sql in equipment_required_columns:
            if col_name not in eq_columns:
                logger.info(f"Adding {col_name} column to equipment table")
                db_debug_log(f"Adding {col_name} column to equipment table")
                cursor.execute(alter_sql)
                logger.info(f"{col_name} column added successfully")
                db_debug_log(f"{col_name} column added successfully")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_equipment_ba_number ON equipment(ba_number)")

        # Table: maintenance
        maintenance_required_columns = [
            ('completion_notes', "ALTER TABLE maintenance ADD COLUMN completion_notes TEXT"),
            ('status', "ALTER TABLE maintenance ADD COLUMN status TEXT DEFAULT 'scheduled'"),
            ('completed_by', "ALTER TABLE maintenance ADD COLUMN completed_by TEXT"),
            ('actual_completion_date', "ALTER TABLE maintenance ADD COLUMN actual_completion_date TEXT"),
            ('completion_meterage', "ALTER TABLE maintenance ADD COLUMN completion_meterage REAL"),
            ('maintenance_category', "ALTER TABLE maintenance ADD COLUMN maintenance_category TEXT DEFAULT 'TM-1'")
        ]
        cursor.execute("PRAGMA table_info(maintenance)")
        maint_columns = [col['name'] for col in cursor.fetchall()]
        for col_name, alter_sql in maintenance_required_columns:
            if col_name not in maint_columns:
                logger.info(f"Adding {col_name} column to maintenance table")
                db_debug_log(f"Adding {col_name} column to maintenance table")
                cursor.execute(alter_sql)
                logger.info(f"{col_name} column added successfully")
                db_debug_log(f"{col_name} column added successfully")

        # Table: repairs
        repairs_required_columns = [
            ('description', "ALTER TABLE repairs ADD COLUMN description TEXT"),
            ('repair_date', "ALTER TABLE repairs ADD COLUMN repair_date TEXT")
        ]
        cursor.execute("PRAGMA table_info(repairs)")
        rep_columns = [col['name'] for col in cursor.fetchall()]
        for col_name, alter_sql in repairs_required_columns:
            if col_name not in rep_columns:
                logger.info(f"Adding {col_name} column to repairs table")
                db_debug_log(f"Adding {col_name} column to repairs table")
                cursor.execute(alter_sql)
                logger.info(f"{col_name} column added successfully")
                db_debug_log(f"{col_name} column added successfully")

        # Table: fluids (full migration)
        fluids_required_columns = [
            ('equipment_id', "ALTER TABLE fluids ADD COLUMN equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE"),
            ('fluid_type', "ALTER TABLE fluids ADD COLUMN fluid_type TEXT NOT NULL"),
            ('sub_type', "ALTER TABLE fluids ADD COLUMN sub_type TEXT"),
            ('accounting_unit', "ALTER TABLE fluids ADD COLUMN accounting_unit TEXT DEFAULT 'Ltr'"),
            ('capacity_ltrs_kg', "ALTER TABLE fluids ADD COLUMN capacity_ltrs_kg REAL DEFAULT 0"),
            ('addl_10_percent_top_up', "ALTER TABLE fluids ADD COLUMN addl_10_percent_top_up REAL DEFAULT 0"),
            ('grade', "ALTER TABLE fluids ADD COLUMN grade TEXT"),
            ('periodicity_km', "ALTER TABLE fluids ADD COLUMN periodicity_km INTEGER DEFAULT 0"),
            ('periodicity_hrs', "ALTER TABLE fluids ADD COLUMN periodicity_hrs INTEGER DEFAULT 0"),
            ('periodicity_months', "ALTER TABLE fluids ADD COLUMN periodicity_months INTEGER DEFAULT 0"),
            ('last_serviced_date', "ALTER TABLE fluids ADD COLUMN last_serviced_date TEXT"),
            ('last_serviced_meterage', "ALTER TABLE fluids ADD COLUMN last_serviced_meterage REAL DEFAULT 0"),
            ('top_up_percent', "ALTER TABLE fluids ADD COLUMN top_up_percent REAL DEFAULT NULL"),
            ('date_of_change', "ALTER TABLE fluids ADD COLUMN date_of_change TEXT")
        ]
        cursor.execute("PRAGMA table_info(fluids)")
        flu_columns = [col['name'] for col in cursor.fetchall()]
        for col_name, alter_sql in fluids_required_columns:
            if col_name not in flu_columns:
                logger.info(f"Adding {col_name} column to fluids table")
                db_debug_log(f"Adding {col_name} column to fluids table")
                cursor.execute(alter_sql)
                logger.info(f"{col_name} column added successfully")
                db_debug_log(f"{col_name} column added successfully")

        # Table: overhauls (robust migration)
        try:
            cursor.execute("PRAGMA table_info(overhauls)")
            oh_columns = [col['name'] for col in cursor.fetchall()]
            overhauls_required_columns = [
                ('overhaul_type', "ALTER TABLE overhauls ADD COLUMN overhaul_type TEXT"),
                ('overhaul_date', "ALTER TABLE overhauls ADD COLUMN overhaul_date DATE"),
                ('done_date', "ALTER TABLE overhauls ADD COLUMN done_date DATE"),
                ('due_date', "ALTER TABLE overhauls ADD COLUMN due_date DATE"),
                ('status', "ALTER TABLE overhauls ADD COLUMN status TEXT"),
                ('meter_reading', "ALTER TABLE overhauls ADD COLUMN meter_reading REAL"),
                ('hours_reading', "ALTER TABLE overhauls ADD COLUMN hours_reading REAL"),
                ('notes', "ALTER TABLE overhauls ADD COLUMN notes TEXT"),
                ('description', "ALTER TABLE overhauls ADD COLUMN description TEXT")
            ]
            for col_name, alter_sql in overhauls_required_columns:
                if col_name not in oh_columns:
                    logger.info(f"Adding {col_name} column to overhauls table")
                    db_debug_log(f"Adding {col_name} column to overhauls table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate overhauls table: {e}")
            db_debug_log(f"Could not check or migrate overhauls table: {e}")

        # Table: discrepancies
        discrepancies_required_columns = [
            ('fluid_type', "ALTER TABLE discrepancies ADD COLUMN fluid_type TEXT"),
            ('sub_type', "ALTER TABLE discrepancies ADD COLUMN sub_type TEXT"),
            ('difference_ltrs_kg', "ALTER TABLE discrepancies ADD COLUMN difference_ltrs_kg REAL DEFAULT 0"),
            ('notes', "ALTER TABLE discrepancies ADD COLUMN notes TEXT"),
            ('reported_date', "ALTER TABLE discrepancies ADD COLUMN reported_date TEXT DEFAULT (date('now'))"),
            ('resolved', "ALTER TABLE discrepancies ADD COLUMN resolved INTEGER DEFAULT 0")
        ]
        try:
            cursor.execute("PRAGMA table_info(discrepancies)")
            disc_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in discrepancies_required_columns:
                if col_name not in disc_columns:
                    logger.info(f"Adding {col_name} column to discrepancies table")
                    db_debug_log(f"Adding {col_name} column to discrepancies table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate discrepancies table: {e}")
            db_debug_log(f"Could not check or migrate discrepancies table: {e}")

        # Table: discard_criteria (equipment-level only, no components)
        try:
            cursor.execute("PRAGMA table_info(discard_criteria)")
            disc_crit_columns_info = cursor.fetchall()
            disc_crit_columns = [col['name'] for col in disc_crit_columns_info]
            
            # Check if component column exists and is NOT NULL
            component_col_info = next((col for col in disc_crit_columns_info if col['name'] == 'component'), None)
            if component_col_info and component_col_info['notnull'] == 1:
                logger.info("Migrating discard_criteria table to make component column nullable")
                db_debug_log("Migrating discard_criteria table to make component column nullable")
                
                # SQLite doesn't support ALTER COLUMN, so we need to recreate the table
                # 1. Create new table with nullable component
                cursor.execute("""
                    CREATE TABLE discard_criteria_new (
                        discard_criteria_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                        component TEXT,
                        criteria_years INTEGER DEFAULT 0,
                        criteria_kms INTEGER DEFAULT 0
                    )
                """)
                
                # 2. Copy data from old table to new table
                cursor.execute("""
                    INSERT INTO discard_criteria_new (discard_criteria_id, equipment_id, component, criteria_years, criteria_kms)
                    SELECT discard_criteria_id, equipment_id, component, criteria_years, criteria_kms
                    FROM discard_criteria
                """)
                
                # 3. Drop old table
                cursor.execute("DROP TABLE discard_criteria")
                
                # 4. Rename new table to old name
                cursor.execute("ALTER TABLE discard_criteria_new RENAME TO discard_criteria")
                
                logger.info("Successfully migrated discard_criteria table")
                db_debug_log("Successfully migrated discard_criteria table")
            
            # Add any missing columns
            discard_required_columns = [
                ('criteria_years', "ALTER TABLE discard_criteria ADD COLUMN criteria_years INTEGER DEFAULT 0"),
                ('criteria_kms', "ALTER TABLE discard_criteria ADD COLUMN criteria_kms INTEGER DEFAULT 0")
            ]
            
            # Refresh column list after potential migration
            cursor.execute("PRAGMA table_info(discard_criteria)")
            disc_crit_columns = [col['name'] for col in cursor.fetchall()]
            
            for col_name, alter_sql in discard_required_columns:
                if col_name not in disc_crit_columns:
                    logger.info(f"Adding {col_name} column to discard_criteria table")
                    db_debug_log(f"Adding {col_name} column to discard_criteria table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate discard_criteria table: {e}")
            db_debug_log(f"Could not check or migrate discard_criteria table: {e}")

        # Table: tyre_maintenance
        tyre_required_columns = [
            ('tyre_rotation_kms', "ALTER TABLE tyre_maintenance ADD COLUMN tyre_rotation_kms INTEGER DEFAULT 0"),
            ('tyre_condition_kms', "ALTER TABLE tyre_maintenance ADD COLUMN tyre_condition_kms INTEGER DEFAULT 0"),
            ('tyre_condition_years', "ALTER TABLE tyre_maintenance ADD COLUMN tyre_condition_years INTEGER DEFAULT 0"),
            ('last_rotation_date', "ALTER TABLE tyre_maintenance ADD COLUMN last_rotation_date TEXT"),
            ('date_of_change', "ALTER TABLE tyre_maintenance ADD COLUMN date_of_change TEXT"),
            ('quantity', "ALTER TABLE tyre_maintenance ADD COLUMN quantity INTEGER DEFAULT 0")
        ]
        try:
            cursor.execute("PRAGMA table_info(tyre_maintenance)")
            tyre_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in tyre_required_columns:
                if col_name not in tyre_columns:
                    logger.info(f"Adding {col_name} column to tyre_maintenance table")
                    db_debug_log(f"Adding {col_name} column to tyre_maintenance table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate tyre_maintenance table: {e}")
            db_debug_log(f"Could not check or migrate tyre_maintenance table: {e}")

        # Table: demand_forecast
        demand_required_columns = [
            ('fluid_id', "ALTER TABLE demand_forecast ADD COLUMN fluid_id INTEGER REFERENCES fluids(fluid_id) ON DELETE CASCADE"),
            ('fiscal_year', "ALTER TABLE demand_forecast ADD COLUMN fiscal_year TEXT NOT NULL"),
            ('total_requirement', "ALTER TABLE demand_forecast ADD COLUMN total_requirement REAL DEFAULT 0"),
            ('remarks', "ALTER TABLE demand_forecast ADD COLUMN remarks TEXT")
        ]
        try:
            cursor.execute("PRAGMA table_info(demand_forecast)")
            demand_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in demand_required_columns:
                if col_name not in demand_columns:
                    logger.info(f"Adding {col_name} column to demand_forecast table")
                    db_debug_log(f"Adding {col_name} column to demand_forecast table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate demand_forecast table: {e}")
            db_debug_log(f"Could not check or migrate demand_forecast table: {e}")

        # Table: conditioning (full migration)
        conditioning_required_columns = [
            ('conditioning_id', "ALTER TABLE conditioning ADD COLUMN conditioning_id INTEGER PRIMARY KEY AUTOINCREMENT"),
            ('equipment_id', "ALTER TABLE conditioning ADD COLUMN equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE"),
            ('fiscal_year', "ALTER TABLE conditioning ADD COLUMN fiscal_year TEXT NOT NULL"),
            ('conditioning_type', "ALTER TABLE conditioning ADD COLUMN conditioning_type TEXT NOT NULL"),
            ('total_requirement', "ALTER TABLE conditioning ADD COLUMN total_requirement REAL DEFAULT 0"),
            ('remarks', "ALTER TABLE conditioning ADD COLUMN remarks TEXT")
        ]
        try:
            cursor.execute("PRAGMA table_info(conditioning)")
            cond_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in conditioning_required_columns:
                if col_name not in cond_columns:
                    logger.info(f"Adding {col_name} column to conditioning table")
                    db_debug_log(f"Adding {col_name} column to conditioning table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate conditioning table: {e}")
            db_debug_log(f"Could not check or migrate conditioning table: {e}")

        # Table: settings
        settings_required_columns = [
            ('settings_id', "ALTER TABLE settings ADD COLUMN settings_id INTEGER PRIMARY KEY AUTOINCREMENT"),
            ('key', "ALTER TABLE settings ADD COLUMN key TEXT UNIQUE NOT NULL"),
            ('value', "ALTER TABLE settings ADD COLUMN value TEXT"),
            ('description', "ALTER TABLE settings ADD COLUMN description TEXT"),
            ('created_at', "ALTER TABLE settings ADD COLUMN created_at TEXT DEFAULT (datetime('now'))"),
            ('updated_at', "ALTER TABLE settings ADD COLUMN updated_at TEXT DEFAULT (datetime('now'))")
        ]
        try:
            cursor.execute("PRAGMA table_info(settings)")
            settings_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in settings_required_columns:
                if col_name not in settings_columns:
                    logger.info(f"Adding {col_name} column to settings table")
                    db_debug_log(f"Adding {col_name} column to settings table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate settings table: {e}")
            db_debug_log(f"Could not check or migrate settings table: {e}")

        connection.commit()
        return True
    except Exception as e:
        logger.error(f"Error updating schema: {e}\n{traceback.format_exc()}")
        db_debug_log(f"Error updating schema: {e}\n{traceback.format_exc()}")
        connection.rollback()
        return False

def init_db():
    """Initialize the database with the tables if they don't exist. Returns (success, first_run)"""
    import traceback
    first_run = False
    # Ensure directory exists
    db_dir = Path(DB_PATH).parent
    db_dir.mkdir(parents=True, exist_ok=True)
    # Check if DB file exists before
    if not os.path.exists(DB_PATH):
        first_run = True
    connection = get_db_connection()
    if not connection:
        logger.error("Failed to initialize database connection")
        return False, first_run

    try:
        cursor = connection.cursor()
        # Equipment Table
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS equipment (
                equipment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                serial_number TEXT,
                make_and_type TEXT NOT NULL,
                units_held INTEGER DEFAULT 1,
                vintage_years REAL DEFAULT 0,
                meterage_kms REAL DEFAULT 0,
                meterage_description TEXT,
                km_hrs_run_previous_month REAL DEFAULT 0,
                km_hrs_run_current_month REAL DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                remarks TEXT,
                section TEXT,
                unit TEXT,
                location TEXT,
                last_service_date TEXT,
                next_service_date TEXT,
                status TEXT,
                ba_number TEXT,
                hours_run_total REAL DEFAULT 0,
                hours_run_previous_month REAL DEFAULT 0,
                hours_run_current_month REAL DEFAULT 0
            )
            """
        )
        cursor.execute(
            """
            CREATE INDEX IF NOT EXISTS idx_equipment_ba_number ON equipment(ba_number)
            """
        )
        # Fluids Table
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS fluids (
                fluid_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                fluid_type TEXT NOT NULL,
                sub_type TEXT,
                accounting_unit TEXT DEFAULT 'Ltr',
                capacity_ltrs_kg REAL DEFAULT 0,
                addl_10_percent_top_up REAL DEFAULT 0,
                grade TEXT,
                periodicity_km INTEGER DEFAULT 0,
                periodicity_hrs INTEGER DEFAULT 0,
                periodicity_months INTEGER DEFAULT 0,
                last_serviced_date TEXT,
                last_serviced_meterage REAL DEFAULT 0,
                date_of_change TEXT
            )
        """)

        # Create maintenance table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS maintenance (
                maintenance_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                maintenance_type TEXT NOT NULL,
                done_date TEXT,
                due_date TEXT,
                vintage_years REAL DEFAULT 0,
                meterage_kms REAL DEFAULT 0,
                completion_notes TEXT,
                status TEXT DEFAULT 'scheduled',
                completed_by TEXT,
                actual_completion_date TEXT,
                completion_meterage REAL,
                maintenance_category TEXT DEFAULT 'TM-1'
            )
        """)

        # Create repairs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS repairs (
                repair_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                repair_type TEXT NOT NULL,
                description TEXT,
                repair_date TEXT
            )
        """)

        # Create discrepancies table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS discrepancies (
                discrepancy_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                fluid_type TEXT NOT NULL,
                sub_type TEXT,
                difference_ltrs_kg REAL DEFAULT 0,
                notes TEXT,
                reported_date TEXT DEFAULT (date('now')),
                resolved INTEGER DEFAULT 0
            )
        """)

        # Create discard_criteria table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS discard_criteria (
                discard_criteria_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                component TEXT NOT NULL,
                criteria_years INTEGER DEFAULT 0,
                criteria_kms INTEGER DEFAULT 0
            )
        """)

        # Create tyre_maintenance table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tyre_maintenance (
                tyre_maintenance_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                tyre_rotation_kms INTEGER DEFAULT 0,
                tyre_condition_kms INTEGER DEFAULT 0,
                tyre_condition_years INTEGER DEFAULT 0,
                last_rotation_date TEXT,
                date_of_change TEXT,
                quantity INTEGER DEFAULT 0
            )
        """)

        # Create demand_forecast table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS demand_forecast (
                demand_id INTEGER PRIMARY KEY AUTOINCREMENT,
                fluid_id INTEGER REFERENCES fluids(fluid_id) ON DELETE CASCADE,
                fiscal_year TEXT NOT NULL,
                total_requirement REAL DEFAULT 0,
                remarks TEXT
            )
        """)

        # Create tyre_forecast table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tyre_forecast (
                forecast_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                fiscal_year TEXT NOT NULL,
                tyre_type TEXT NOT NULL,
                quantity_required INTEGER DEFAULT 0,
                total_requirement REAL DEFAULT 0,
                remarks TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now'))
            )
        """)

        # Create battery_forecast table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS battery_forecast (
                forecast_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                fiscal_year TEXT NOT NULL,
                battery_type TEXT NOT NULL,
                voltage REAL DEFAULT 0,
                quantity_required INTEGER DEFAULT 0,
                total_requirement REAL DEFAULT 0,
                remarks TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now'))
            )
        """)

        # Create equipment_forecast table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS equipment_forecast (
                forecast_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                fiscal_year TEXT NOT NULL,
                equipment_type TEXT NOT NULL,
                replacement_reason TEXT,
                quantity_required INTEGER DEFAULT 1,
                total_requirement REAL DEFAULT 0,
                remarks TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now'))
            )
        """)

        # Create conditioning table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS conditioning (
                conditioning_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                fiscal_year TEXT NOT NULL,
                conditioning_type TEXT NOT NULL,
                total_requirement REAL DEFAULT 0,
                remarks TEXT
            )
        """)
        
        # Create medium_resets table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS medium_resets (
                medium_reset_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                reset_type TEXT NOT NULL,
                done_date TEXT,
                due_date TEXT,
                mr1_due_date TEXT,
                oh1_due_date TEXT,
                mr2_due_date TEXT,
                oh2_due_date TEXT,
                discard_due_date TEXT,
                status TEXT DEFAULT 'scheduled',
                meter_reading INTEGER DEFAULT 0,
                hours_reading INTEGER DEFAULT 0,
                description TEXT,
                notes TEXT
            )
        """)

        # Create overhauls table with comprehensive schema
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS overhauls (
                overhaul_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                overhaul_type TEXT NOT NULL CHECK (overhaul_type IN ('OH-I', 'OH-II')),
                overhaul_date TEXT,  -- Legacy field for compatibility
                done_date TEXT,
                due_date TEXT,
                status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'overdue', 'warning', 'critical', 'discard', 'unknown')),
                meter_reading REAL DEFAULT 0,
                hours_reading REAL DEFAULT 0,
                notes TEXT,
                description TEXT,
                created_date TEXT DEFAULT (datetime('now')),
                updated_date TEXT DEFAULT (datetime('now')),
                completed_by TEXT,
                completion_notes TEXT
            )
        """)

        # Create battery table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS battery (
                battery_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                battery_type TEXT NOT NULL,
                voltage REAL DEFAULT 0,
                capacity_ah REAL DEFAULT 0,
                date_of_installation TEXT,
                date_of_replacement TEXT,
                status TEXT DEFAULT 'active',
                notes TEXT
            )
        """)

        # Create maintenance_archives table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS maintenance_archives (
                archive_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                maintenance_type TEXT NOT NULL,
                done_date TEXT,
                due_date TEXT,
                vintage_years REAL DEFAULT 0,
                meterage_kms REAL DEFAULT 0,
                completion_notes TEXT,
                status TEXT DEFAULT 'completed',
                completed_by TEXT,
                actual_completion_date TEXT,
                completion_meterage REAL,
                maintenance_category TEXT DEFAULT 'TM-1',
                archived_date TEXT DEFAULT (datetime('now')),
                archived_by TEXT
            )
        """)

        # Create settings table for application settings
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS settings (
                settings_id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now'))
            )
        """)

        # Commit changes
        connection.commit()
        logger.info("Database tables initialized successfully")
        
        # Check and update schema for any missing columns
        if not check_and_update_schema(connection):
            logger.error("Failed to update database schema")
            close_db_connection(connection)
            return False, first_run

        close_db_connection(connection)
        return True, first_run
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        if connection:
            connection.rollback()
            db_debug_log("Rolled back changes due to error")
        close_db_connection(connection)
        db_debug_log("Database connection closed")
        return False, first_run


def execute_query(query, params=(), fetchall=True):
    """Execute a SQL query and return results.

    Args:
        query (str): The SQL query to execute.
        params (tuple): Parameters for the query.
        fetchall (bool): If True, fetch all rows; if False, fetch one row.

    Returns:
        Results of the query (list of dicts if fetchall=True, single dict if fetchall=False, rowcount for updates).
    """
    start_time = time.time()
    connection = None
    cursor = None
    
    try:
        logger.info("Starting transaction for query execution (no lock used)")
        db_debug_log("Starting transaction for query execution (no lock used)")
        connection = get_db_connection()
        db_debug_log("Database connection opened")
        if not connection:
            logger.error("Could not get database connection")
            db_debug_log("Could not get database connection")
            return None
        
        cursor = connection.cursor()
        db_debug_log("Database cursor created")
        logger.info(f"Executing query: {query}")
        db_debug_log(f"Executing query: {query} | Params: {params}")
        logger.info(f"Parameters: {params}")
        
        # Convert query for SQLite compatibility
        query = query.replace("%s", "?")
        
        cursor.execute(query, params)
        
        # Check if this is an INSERT/UPDATE/DELETE statement
        is_dml = query.strip().upper().startswith(('INSERT', 'UPDATE', 'DELETE'))
        has_returning = 'RETURNING' in query.upper()
        if is_dml:
            if has_returning:
                row = cursor.fetchone()
                result = dict(row) if row else None
                connection.commit()
                logger.info("Transaction committed for DML RETURNING operation")
                return result
            else:
                affected = cursor.rowcount
                connection.commit()
                logger.info("Transaction committed for DML operation")
                return affected
        
        # For SELECT statements
        if fetchall:
            rows = cursor.fetchall()
            result = [dict(row) for row in rows]
        else:
            row = cursor.fetchone()
            result = dict(row) if row else None
        
        elapsed_time = time.time() - start_time
        logger.info(f"Query completed in {elapsed_time:.3f} seconds. Rows returned: {len(result) if isinstance(result, list) else 1 if result else 0}")
        logger.info("Ending transaction for query execution")
        return result
    except Exception as e:
        logger.error(f"Database error: {e}")
        if connection:
            connection.rollback()
            logger.info("Transaction rolled back due to error")
        raise
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
            logger.debug("Closed SQLite connection")


def execute_insert(query, params=None):
    """Execute an insert query and return the inserted ID."""
    if params is None:
        params = ()
    
    connection = None
    cursor = None
    try:
        logger.info("Starting transaction for insert operation (no lock used)")
        connection = get_db_connection()
        if not connection:
            logger.error("Could not get database connection")
            return None
        cursor = connection.cursor()
        logger.info(f"Executing insert query: {query}")
        logger.info(f"Parameters: {params}")
        
        # Convert query for SQLite compatibility
        query = query.replace("%s", "?")
        has_returning = 'RETURNING' in query.upper()
        cursor.execute(query, params)
        if has_returning:
            row = cursor.fetchone()
            inserted_id = dict(row).get('equipment_id') if row else None
            connection.commit()
            logger.info("Insert committed (RETURNING)")
            return inserted_id
        else:
            connection.commit()
            cursor.execute("SELECT last_insert_rowid()")
            inserted_id = cursor.fetchone()["last_insert_rowid()"]
            logger.info("Insert committed, id fetched via last_insert_rowid()")
            return inserted_id
    except Exception as e:
        logger.error(f"Database insert error: {e}")
        if connection:
            connection.rollback()
            logger.info("Insert transaction rolled back due to error")
        raise
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
            logger.debug("Closed SQLite connection")


def execute_update(query, params=None):
    """Execute an update query and return the number of affected rows."""
    if params is None:
        params = ()
    
    connection = None
    cursor = None
    try:
        logger.info("Starting transaction for update operation (no lock used)")
        connection = get_db_connection()
        if not connection:
            logger.error("Could not get database connection")
            return None
        cursor = connection.cursor()
        logger.info(f"Executing update query: {query}")
        logger.info(f"Parameters: {params}")
        
        # Convert query for SQLite compatibility
        query = query.replace("%s", "?")
        has_returning = 'RETURNING' in query.upper()
        cursor.execute(query, params)
        if has_returning:
            row = cursor.fetchone()
            result = dict(row) if row else None
            connection.commit()
            logger.info("Update committed (RETURNING)")
            return result
        else:
            affected = cursor.rowcount
            connection.commit()
            logger.info("Update committed")
            return affected
    except Exception as e:
        logger.error(f"Database update error: {e}")
        if connection:
            connection.rollback()
            logger.info("Update transaction rolled back due to error")
        raise
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
            logger.debug("Closed SQLite connection")