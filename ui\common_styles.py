"""
Common UI styles for the Army Equipment Inventory Management System.
This module provides consistent styling across all tabs based on the Maintenance tab design.
"""

# Common button styles following Maintenance tab design
BUTTON_STYLE = """
QPushButton {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

QPushButton:pressed {
    background-color: #dee2e6;
    border-color: #6c757d;
}

QPushButton:disabled {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}
"""

# Primary action button style (Save, Add New, etc.)
PRIMARY_BUTTON_STYLE = """
QPushButton {
    background-color: #007bff;
    border: 1px solid #007bff;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    color: white;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

QPushButton:pressed {
    background-color: #004085;
    border-color: #004085;
}

QPushButton:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}
"""

# Danger button style (Delete, Delete All, etc.)
DANGER_BUTTON_STYLE = """
QPushButton {
    background-color: #dc3545;
    border: 1px solid #dc3545;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    color: white;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

QPushButton:pressed {
    background-color: #a71e2a;
    border-color: #a71e2a;
}

QPushButton:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}
"""

# Success button style (Complete, Send to Maintenance, etc.)
SUCCESS_BUTTON_STYLE = """
QPushButton {
    background-color: #28a745;
    border: 1px solid #28a745;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    color: white;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

QPushButton:pressed {
    background-color: #1e7e34;
    border-color: #1c7430;
}

QPushButton:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}
"""

# Form group box style
FORM_GROUP_STYLE = """
QGroupBox {
    font-weight: bold;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    margin-top: 1ex;
    padding-top: 10px;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #495057;
    font-size: 14px;
}
"""

# Input field styles
INPUT_FIELD_STYLE = """
QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox, QDateEdit {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    background-color: #ffffff;
    color: #495057;
}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus, QDateEdit:focus {
    border-color: #80bdff;
    outline: 0;
}

QLineEdit:disabled, QTextEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled, QComboBox:disabled, QDateEdit:disabled {
    background-color: #e9ecef;
    color: #6c757d;
    border-color: #dee2e6;
}

QLineEdit[readOnly="true"], QTextEdit[readOnly="true"] {
    background-color: #f8f9fa;
    color: #6c757d;
}
"""

# Table styles
TABLE_STYLE = """
QTableWidget {
    gridline-color: #dee2e6;
    background-color: #ffffff;
    alternate-background-color: #f8f9fa;
    selection-background-color: #007bff;
    selection-color: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

QTableWidget::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #007bff;
    color: white;
}

QHeaderView::section {
    background-color: #f8f9fa;
    color: #495057;
    padding: 8px;
    border: 1px solid #dee2e6;
    font-weight: bold;
}

QHeaderView::section:hover {
    background-color: #e9ecef;
}
"""

# Label styles
LABEL_STYLE = """
QLabel {
    color: #495057;
    font-size: 14px;
}

QLabel[class="title"] {
    font-size: 18px;
    font-weight: bold;
    color: #212529;
    margin-bottom: 16px;
}

QLabel[class="section-title"] {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
    margin: 12px 0 8px 0;
}
"""

# Layout spacing constants (following Maintenance tab)
LAYOUT_MARGINS = (12, 12, 12, 12)  # left, top, right, bottom
LAYOUT_SPACING = 8
FORM_SPACING = 6
BUTTON_SPACING = 8

# Splitter proportions (following Maintenance tab)
SPLITTER_LEFT_WIDTH = 400
SPLITTER_RIGHT_WIDTH = 600

def get_complete_stylesheet():
    """Get the complete stylesheet for the application."""
    return f"""
    {BUTTON_STYLE}
    {INPUT_FIELD_STYLE}
    {TABLE_STYLE}
    {FORM_GROUP_STYLE}
    {LABEL_STYLE}
    """

def apply_button_style(button, style_type="default"):
    """Apply specific button style to a button widget."""
    if style_type == "primary":
        button.setStyleSheet(PRIMARY_BUTTON_STYLE)
    elif style_type == "danger":
        button.setStyleSheet(DANGER_BUTTON_STYLE)
    elif style_type == "success":
        button.setStyleSheet(SUCCESS_BUTTON_STYLE)
    else:
        button.setStyleSheet(BUTTON_STYLE)

def apply_standard_layout(layout):
    """Apply standard layout spacing and margins."""
    layout.setContentsMargins(*LAYOUT_MARGINS)
    layout.setSpacing(LAYOUT_SPACING)

def apply_form_layout(layout):
    """Apply standard form layout spacing."""
    layout.setContentsMargins(*LAYOUT_MARGINS)
    layout.setVerticalSpacing(FORM_SPACING)
    layout.setHorizontalSpacing(FORM_SPACING)

def apply_button_layout(layout):
    """Apply standard button layout spacing."""
    layout.setSpacing(BUTTON_SPACING)

def apply_status_style(label, status):
    """Apply color-coded styling to status labels."""
    import utils
    color = utils.get_status_color(status)

    label.setStyleSheet(f"""
        QLabel {{
            background-color: {color};
            color: white;
            border-radius: 5px;
            padding: 4px 8px;
            font-weight: bold;
            text-align: center;
        }}
    """)
