"""Data models for the equipment inventory application."""
import logging
from datetime import datetime, date, timedelta

import database
import utils

# Configure logger
logger = logging.getLogger('models')

class Equipment:
    """Equipment model representing the equipment table."""
    
    def __init__(self, equipment_id=None, serial_number=None, make_and_type=None, 
                 ba_number=None, units_held=1, vintage_years=0.0, meterage_kms=0.0, 
                 meterage_description=None, km_hrs_run_previous_month=0.0, km_hrs_run_current_month=0.0, 
                 hours_run_total=0.0, hours_run_previous_month=0.0, hours_run_current_month=0.0,
                 is_active=True, remarks=None, auto_update_totals=False, date_of_commission=None,
                 oh1_done_date=None, oh1_due_date=None, oh2_done_date=None, oh2_due_date=None,
                 date_of_induction=None):
        """Initialize an Equipment instance."""
        self.equipment_id = equipment_id
        self.serial_number = serial_number
        self.make_and_type = make_and_type
        self.ba_number = ba_number
        self.units_held = units_held
        self.vintage_years = vintage_years
        self.date_of_commission = date_of_commission
        self.oh1_done_date = oh1_done_date
        self.oh1_due_date = oh1_due_date
        self.oh2_done_date = oh2_done_date
        self.oh2_due_date = oh2_due_date
        self.date_of_induction = date_of_induction
        
        # Original total values
        self._original_meterage_kms = meterage_kms
        self._original_hours_run_total = hours_run_total
        
        # Current values
        self.meterage_kms = meterage_kms
        self.meterage_description = meterage_description
        self.km_hrs_run_previous_month = km_hrs_run_previous_month
        self.km_hrs_run_current_month = km_hrs_run_current_month
        self.hours_run_total = hours_run_total
        self.hours_run_previous_month = hours_run_previous_month
        self.hours_run_current_month = hours_run_current_month
        
        # If auto-update is on, apply changes in current month to total
        if auto_update_totals and self.equipment_id:
            self.apply_current_month_to_totals()
            
        self.is_active = is_active
        self.remarks = remarks
        
    def apply_current_month_to_totals(self):
        """Apply current month values to totals.
        
        This method is used when current month values have been manually entered
        and we want to automatically update the totals accordingly.
        """
        if self.equipment_id:  # Only for existing equipment
            # Get previous values from database
            query = "SELECT meterage_kms, hours_run_total, km_hrs_run_current_month, hours_run_current_month FROM equipment WHERE equipment_id = %s"
            result = database.execute_query(query, (self.equipment_id,), fetchall=False)
            
            if result:
                old_meterage = float(result['meterage_kms'] or 0)
                old_hours = float(result['hours_run_total'] or 0)
                old_km_current = float(result['km_hrs_run_current_month'] or 0)
                old_hours_current = float(result['hours_run_current_month'] or 0)
                
                # Calculate differences in current month values
                km_diff = self.km_hrs_run_current_month - old_km_current
                hours_diff = self.hours_run_current_month - old_hours_current
                
                # Update totals with the differences
                if km_diff != 0:
                    self.meterage_kms = old_meterage + km_diff
                    logger.info(f"Auto-updated meterage total for equipment ID {self.equipment_id} by {km_diff} km")
                    
                if hours_diff != 0:
                    self.hours_run_total = old_hours + hours_diff
                    logger.info(f"Auto-updated hours total for equipment ID {self.equipment_id} by {hours_diff} hrs")
        
        return self
    
    @staticmethod
    def get_all():
        """Get all equipment records."""
        query = """
            SELECT * FROM equipment
            ORDER BY make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_active():
        """Get all active equipment records."""
        query = """
            SELECT * FROM equipment
            WHERE is_active = TRUE
            ORDER BY make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_id(equipment_id):
        """Get equipment by ID."""
        query = """
            SELECT * FROM equipment
            WHERE equipment_id = %s
        """
        result = database.execute_query(query, (equipment_id,), fetchall=False)
        if result:
            # Convert database dictionary to Equipment object
            equipment = Equipment(
                equipment_id=result['equipment_id'],
                serial_number=result['serial_number'],
                make_and_type=result['make_and_type'],
                ba_number=result['ba_number'],
                units_held=result['units_held'],
                vintage_years=result['vintage_years'],
                meterage_kms=result['meterage_kms'],
                meterage_description=result.get('meterage_description'),
                km_hrs_run_previous_month=result['km_hrs_run_previous_month'],
                km_hrs_run_current_month=result['km_hrs_run_current_month'],
                hours_run_total=result.get('hours_run_total', 0),
                hours_run_previous_month=result.get('hours_run_previous_month', 0),
                hours_run_current_month=result.get('hours_run_current_month', 0),
                is_active=result['is_active'],
                remarks=result['remarks'],
                date_of_commission=result.get('date_of_commission')
            )
            # The line "equipment.date_of_rel = result.get('date_of_rel')" should be removed.
            return equipment
        return None
    
    @staticmethod
    def get_by_serial(serial_number):
        """Get equipment by serial number."""
        query = """
            SELECT * FROM equipment
            WHERE serial_number = %s
        """
        return database.execute_query(query, (serial_number,), fetchall=False)
    
    @staticmethod
    def get_by_ba_number(ba_number):
        """Get equipment by BA number."""
        query = """
            SELECT * FROM equipment
            WHERE ba_number = %s
        """
        return database.execute_query(query, (ba_number,), fetchall=False)
    
    @staticmethod
    def search_by_ba_number(search_term):
        """Search equipment by partial BA number match."""
        query = """
            SELECT * FROM equipment
            WHERE ba_number LIKE %s
            ORDER BY ba_number
        """
        return database.execute_query(query, (f"%{search_term}%",))
    
    def save(self):
        """Save equipment to database (insert or update)."""
        if self.equipment_id:
            # Update existing record
            query = """
                UPDATE equipment
                SET serial_number = %s,
                    make_and_type = %s,
                    ba_number = %s,
                    units_held = %s,
                    vintage_years = %s,
                    meterage_kms = %s,
                    meterage_description = %s,
                    km_hrs_run_previous_month = %s,
                    km_hrs_run_current_month = %s,
                    hours_run_total = %s,
                    hours_run_previous_month = %s,
                    hours_run_current_month = %s,
                    is_active = %s,
                    remarks = %s,
                    date_of_commission = %s
                WHERE equipment_id = %s
                RETURNING equipment_id
            """
            params = (
                self.serial_number,
                self.make_and_type,
                self.ba_number,
                self.units_held,
                self.vintage_years,
                self.meterage_kms,
                self.meterage_description,
                self.km_hrs_run_previous_month,
                self.km_hrs_run_current_month,
                self.hours_run_total,
                self.hours_run_previous_month,
                self.hours_run_current_month,
                self.is_active,
                self.remarks,
                self.date_of_commission,
                self.equipment_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['equipment_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO equipment (
                    serial_number, make_and_type, ba_number, units_held, 
                    vintage_years, meterage_kms, meterage_description, km_hrs_run_previous_month, 
                    km_hrs_run_current_month, hours_run_total, hours_run_previous_month,
                    hours_run_current_month, is_active, remarks, date_of_commission
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                RETURNING equipment_id
            """
            params = (self.serial_number, self.make_and_type, self.ba_number, 
                    self.units_held, self.vintage_years, self.meterage_kms, self.meterage_description,
                    self.km_hrs_run_previous_month, self.km_hrs_run_current_month, 
                    self.hours_run_total, self.hours_run_previous_month, self.hours_run_current_month,
                    self.is_active, self.remarks, self.date_of_commission)
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.equipment_id = result['equipment_id']
                return self.equipment_id
            return None
    
    @staticmethod
    def delete(equipment_id):
        """Delete equipment by ID."""
        query = """
            DELETE FROM equipment
            WHERE equipment_id = ?
        """
        database.execute_query(query, (equipment_id,), fetchall=False)
        return equipment_id

    @staticmethod
    def delete_all():
        """Delete all equipment records (and all related records via ON DELETE CASCADE)."""
        query = "DELETE FROM equipment"
        database.execute_query(query, fetchall=False)

import config

class Fluid:
    """Fluid model representing the fluids table."""
    
    def __init__(self, fluid_id=None, equipment_id=None, assembly=None, 
                 accounting_unit="Ltr", capacity_ltrs_kg=0.0,
                 addl_10_percent_top_up=0.0, top_up_percent=None, grade=None, periodicity_km=0,
                 periodicity_hrs=0, periodicity_months=0, last_serviced_date=None,
                 last_serviced_meterage=0.0, fluid_type=None, date_of_change=None):
        """Initialize a Fluid instance. 'assembly' is the public name for 'fluid_type'."""
        self.fluid_id = fluid_id
        self.equipment_id = equipment_id
        self.fluid_type = assembly if assembly is not None else fluid_type
        self.accounting_unit = accounting_unit
        self.capacity_ltrs_kg = capacity_ltrs_kg
        self.addl_10_percent_top_up = addl_10_percent_top_up
        self.top_up_percent = top_up_percent if top_up_percent is not None else None
        self.grade = grade
        self.periodicity_km = periodicity_km
        self.periodicity_hrs = periodicity_hrs
        self.periodicity_months = periodicity_months
        self.last_serviced_date = last_serviced_date
        self.last_serviced_meterage = last_serviced_meterage
        self.date_of_change = date_of_change

    @property
    def effective_top_up_percent(self):
        """Return the top-up percent for this fluid, falling back to global default or legacy value."""
        if self.top_up_percent is not None:
            return self.top_up_percent
        elif self.addl_10_percent_top_up and self.capacity_ltrs_kg:
            # If legacy value exists, infer percent
            return round((self.addl_10_percent_top_up / self.capacity_ltrs_kg) * 100, 2)
        else:
            return config.DEFAULT_TOP_UP_PERCENT

    @property
    def computed_top_up_value(self):
        """Return the computed top-up value (capacity * percent / 100)."""
        return round(self.capacity_ltrs_kg * self.effective_top_up_percent / 100, 3)

    def migrate_legacy_top_up(self):
        """If only addl_10_percent_top_up is set, migrate to top_up_percent."""
        if self.top_up_percent is None and self.addl_10_percent_top_up and self.capacity_ltrs_kg:
            self.top_up_percent = round((self.addl_10_percent_top_up / self.capacity_ltrs_kg) * 100, 2)


    @property
    def assembly(self):
        """Public alias for fluid_type (DB column)."""
        return self.fluid_type

    @assembly.setter
    def assembly(self, value):
        self.fluid_type = value
    
    @staticmethod
    def get_all():
        """Get all fluid records."""
        query = """
            SELECT f.*, e.make_and_type, e.ba_number 
            FROM fluids f
            JOIN equipment e ON f.equipment_id = e.equipment_id
            ORDER BY e.make_and_type, f.fluid_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_equipment(equipment_id):
        """Get fluids for a specific equipment."""
        query = """
            SELECT f.*, e.make_and_type, e.ba_number
            FROM fluids f
            JOIN equipment e ON f.equipment_id = e.equipment_id
            WHERE f.equipment_id = ?
            ORDER BY f.fluid_type
        """
        return database.execute_query(query, (equipment_id,))
    
    @staticmethod
    def get_by_id(fluid_id):
        """Get fluid by ID."""
        query = """
            SELECT f.*, e.make_and_type, e.ba_number
            FROM fluids f
            JOIN equipment e ON f.equipment_id = e.equipment_id
            WHERE f.fluid_id = ?
        """
        return database.execute_query(query, (fluid_id,), fetchall=False)
    
    @staticmethod
    def get_fluid_types():
        """Get list of all fluid types in the system."""
        query = """
            SELECT DISTINCT fluid_type
            FROM fluids
            ORDER BY fluid_type
        """
        return database.execute_query(query)
    
    def save(self):
        """Save fluid to database (insert or update)."""
        if self.fluid_id:
            # Update existing record
            query = """
                UPDATE fluids
                SET equipment_id = %s,
                    fluid_type = %s,
                    accounting_unit = %s,
                    capacity_ltrs_kg = %s,
                    addl_10_percent_top_up = %s,
                    top_up_percent = %s,
                    grade = %s,
                    periodicity_km = %s,
                    periodicity_hrs = %s,
                    periodicity_months = %s,
                    last_serviced_date = %s,
                    last_serviced_meterage = %s,
                    date_of_change = %s
                WHERE fluid_id = %s
                RETURNING fluid_id
            """
            params = (
                self.equipment_id,
                self.fluid_type,
                self.accounting_unit,
                self.capacity_ltrs_kg,
                self.addl_10_percent_top_up,
                self.top_up_percent,
                self.grade,
                self.periodicity_km,
                self.periodicity_hrs,
                self.periodicity_months,
                self.last_serviced_date,
                self.last_serviced_meterage,
                self.date_of_change,
                self.fluid_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['fluid_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO fluids (
                    equipment_id,
                    fluid_type,
                    accounting_unit,
                    capacity_ltrs_kg,
                    addl_10_percent_top_up,
                    top_up_percent,
                    grade,
                    periodicity_km,
                    periodicity_hrs,
                    periodicity_months,
                    last_serviced_date,
                    last_serviced_meterage,
                    date_of_change
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING fluid_id
            """
            params = (
                self.equipment_id,
                self.fluid_type,
                self.accounting_unit,
                self.capacity_ltrs_kg,
                self.addl_10_percent_top_up,
                self.top_up_percent,
                self.grade,
                self.periodicity_km,
                self.periodicity_hrs,
                self.periodicity_months,
                self.last_serviced_date,
                self.last_serviced_meterage,
                self.date_of_change
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.fluid_id = result['fluid_id']
                return self.fluid_id
            return None
    
    @staticmethod
    def delete(fluid_id):
        """Delete fluid by ID."""
        query = """
            DELETE FROM fluids
            WHERE fluid_id = %s
            RETURNING fluid_id
        """
        result = database.execute_query(query, (fluid_id,), fetchall=False)
        return result['fluid_id'] if result else None

    @staticmethod
    def delete_all():
        """Delete all fluids records."""
        database.execute_query("DELETE FROM fluids")

class Maintenance:
    """Maintenance model representing the maintenance table."""
    
    def __init__(self, maintenance_id=None, equipment_id=None, maintenance_type=None,
                 done_date=None, due_date=None, vintage_years=0.0, meterage_kms=0.0,
                 completion_notes=None, status='scheduled', completed_by=None,
                 actual_completion_date=None, completion_meterage=None, maintenance_category='TM-1'):
        """Initialize a Maintenance instance."""
        self.maintenance_id = maintenance_id
        self.equipment_id = equipment_id
        self.maintenance_type = maintenance_type
        self.done_date = done_date
        self.due_date = due_date
        self.vintage_years = vintage_years
        self.meterage_kms = meterage_kms
        self.completion_notes = completion_notes
        self.status = status
        self.completed_by = completed_by
        self.actual_completion_date = actual_completion_date
        self.completion_meterage = completion_meterage
        self.maintenance_category = maintenance_category
    
    @staticmethod
    def get_all():
        """Get all maintenance records."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            ORDER BY m.due_date, e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_equipment(equipment_id):
        """Get maintenance records for a specific equipment."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.equipment_id = %s
            ORDER BY m.due_date
        """
        return database.execute_query(query, (equipment_id,))
    
    @staticmethod
    def get_upcoming(days=30):
        """Get upcoming maintenance (due within specified days)."""
        query = f"""
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.due_date IS NOT NULL 
            AND date(m.due_date) BETWEEN date('now') AND date('now', '+{days} days')
            ORDER BY m.due_date, e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_overdue():
        """Get overdue maintenance records."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.due_date IS NOT NULL 
            AND date(m.due_date) < date('now')
            ORDER BY m.due_date, e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_id(maintenance_id):
        """Get maintenance by ID."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.maintenance_id = ?
        """
        return database.execute_query(query, (maintenance_id,), fetchall=False)
    
    def save(self):
        """Save maintenance to database (insert or update)."""
        if self.maintenance_id:
            # Update existing record
            query = """
                UPDATE maintenance
                SET equipment_id = %s,
                    maintenance_type = %s,
                    done_date = %s,
                    due_date = %s,
                    vintage_years = %s,
                    meterage_kms = %s,
                    completion_notes = %s,
                    status = %s,
                    completed_by = %s,
                    actual_completion_date = %s,
                    completion_meterage = %s,
                    maintenance_category = %s
                WHERE maintenance_id = %s
                RETURNING maintenance_id
            """
            params = (
                self.equipment_id,
                self.maintenance_type,
                self.done_date,
                self.due_date,
                self.vintage_years,
                self.meterage_kms,
                self.completion_notes,
                self.status,
                self.completed_by,
                self.actual_completion_date,
                self.completion_meterage,
                self.maintenance_category,
                self.maintenance_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['maintenance_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO maintenance (
                    equipment_id,
                    maintenance_type,
                    done_date,
                    due_date,
                    vintage_years,
                    meterage_kms,
                    completion_notes,
                    status,
                    completed_by,
                    actual_completion_date,
                    completion_meterage,
                    maintenance_category
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING maintenance_id
            """
            params = (
                self.equipment_id,
                self.maintenance_type,
                self.done_date,
                self.due_date,
                self.vintage_years,
                self.meterage_kms,
                self.completion_notes,
                self.status,
                self.completed_by,
                self.actual_completion_date,
                self.completion_meterage,
                self.maintenance_category
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.maintenance_id = result['maintenance_id']
                return self.maintenance_id
            return None
    
    @staticmethod
    def delete(maintenance_id):
        """Delete maintenance by ID."""
        query = """
            DELETE FROM maintenance
            WHERE maintenance_id = %s
            RETURNING maintenance_id
        """
        result = database.execute_query(query, (maintenance_id,), fetchall=False)
        return result['maintenance_id'] if result else None

    @staticmethod
    def delete_all():
        """Delete all maintenance records."""
        query = "DELETE FROM maintenance"
        database.execute_query(query)
    
    def complete_maintenance(self, completion_date, completion_meterage, notes=None, completed_by=None):
        """Mark maintenance as completed with completion details."""
        self.status = 'completed'
        self.actual_completion_date = completion_date
        self.completion_meterage = completion_meterage
        self.completion_notes = notes
        self.completed_by = completed_by
        self.done_date = completion_date  # Update done_date for backward compatibility
        self.meterage_kms = completion_meterage  # Update meterage for backward compatibility
        
        return self.save()
    
    @staticmethod
    def get_pending():
        """Get all pending (non-completed) maintenance records."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.status != 'completed' OR m.status IS NULL
            ORDER BY m.due_date, e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_completed():
        """Get all completed maintenance records."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.status = 'completed'
            ORDER BY m.actual_completion_date DESC, e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_category(category):
        """Get maintenance records by category (excludes archived records)."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.maintenance_category = %s
            AND (m.status != 'archived' OR m.status IS NULL)
            ORDER BY m.due_date, e.make_and_type
        """
        return database.execute_query(query, (category,))
    
    @staticmethod
    def get_pending_by_category(category):
        """Get pending maintenance records by category."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.maintenance_category = %s
            AND (m.status != 'completed' OR m.status IS NULL)
            AND (m.status != 'archived' OR m.status IS NULL)
            ORDER BY m.due_date, e.make_and_type
        """
        return database.execute_query(query, (category,))
    
    @staticmethod
    def get_upcoming_by_category(category, days=30):
        """Get upcoming maintenance records by category within specified days."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.maintenance_category = %s
            AND m.due_date IS NOT NULL 
            AND date(m.due_date) <= date('now', '+{} days')
            AND (m.status != 'completed' OR m.status IS NULL)
            AND (m.status != 'archived' OR m.status IS NULL)
            ORDER BY m.due_date, e.make_and_type
        """.format(days)
        return database.execute_query(query, (category,))
    
    @staticmethod
    def get_overdue_by_category(category):
        """Get overdue maintenance records by category."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.maintenance_category = %s
            AND m.due_date IS NOT NULL 
            AND date(m.due_date) < date('now')
            AND (m.status != 'completed' OR m.status IS NULL)
            AND (m.status != 'archived' OR m.status IS NULL)
            ORDER BY m.due_date, e.make_and_type
        """
        return database.execute_query(query, (category,))
    
    @staticmethod
    def get_completed_by_category(category):
        """Get completed maintenance records by category."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.maintenance_category = %s
            AND m.status = 'completed'
            ORDER BY m.actual_completion_date DESC, e.make_and_type
        """
        return database.execute_query(query, (category,))
    
    @staticmethod
    def get_completed_by_category_and_date_range(category, start_date, end_date):
        """Get completed maintenance records by category within a date range."""
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.maintenance_category = %s
            AND m.status = 'completed'
            AND m.actual_completion_date BETWEEN %s AND %s
            ORDER BY m.actual_completion_date DESC, e.make_and_type
        """
        return database.execute_query(query, (category, start_date, end_date))
    
    @staticmethod
    def get_for_archiving(category, period_start, period_end, include_pending=False):
        """Get maintenance records suitable for archiving."""
        base_query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.maintenance_category = %s
            AND (m.actual_completion_date BETWEEN %s AND %s
                 OR (m.actual_completion_date IS NULL AND m.done_date BETWEEN %s AND %s))
        """
        
        if include_pending:
            base_query += " ORDER BY m.due_date DESC, e.make_and_type"
            params = (category, period_start, period_end, period_start, period_end)
        else:
            base_query += " AND m.status = 'completed' ORDER BY m.actual_completion_date DESC, e.make_and_type"
            params = (category, period_start, period_end, period_start, period_end)
        
        return database.execute_query(base_query, params)
    
    @staticmethod
    def archive_records(maintenance_ids):
        """Mark maintenance records as archived by setting their status."""
        if not maintenance_ids:
            return 0
        
        # Create placeholders for the IN clause
        placeholders = ','.join(['%s'] * len(maintenance_ids))
        query = f"""
            UPDATE maintenance 
            SET status = 'archived'
            WHERE maintenance_id IN ({placeholders})
            AND status = 'completed'
        """
        
        try:
            result = database.execute_query(query, maintenance_ids, fetchall=False)
            return len(maintenance_ids)  # Return count of records processed
        except Exception as e:
            logger.error(f"Error archiving maintenance records: {e}")
            return 0

class Repair:
    """Repair model representing the repairs table."""
    
    def __init__(self, repair_id=None, equipment_id=None, repair_type=None,
                 description=None, repair_date=None):
        """Initialize a Repair instance."""
        self.repair_id = repair_id
        self.equipment_id = equipment_id
        self.repair_type = repair_type
        self.description = description
        self.repair_date = repair_date
    
    @staticmethod
    def get_all():
        """Get all repair records."""
        query = """
            SELECT r.*, e.make_and_type, e.ba_number
            FROM repairs r
            JOIN equipment e ON r.equipment_id = e.equipment_id
            ORDER BY r.repair_date DESC, e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_equipment(equipment_id):
        """Get repairs for a specific equipment."""
        query = """
            SELECT r.*, e.make_and_type, e.ba_number
            FROM repairs r
            JOIN equipment e ON r.equipment_id = e.equipment_id
            WHERE r.equipment_id = %s
            ORDER BY r.repair_date DESC
        """
        return database.execute_query(query, (equipment_id,))
    
    @staticmethod
    def get_by_id(repair_id):
        """Get repair by ID."""
        query = """
            SELECT r.*, e.make_and_type, e.ba_number
            FROM repairs r
            JOIN equipment e ON r.equipment_id = e.equipment_id
            WHERE r.repair_id = %s
        """
        return database.execute_query(query, (repair_id,), fetchall=False)
    
    def save(self):
        """Save repair to database (insert or update)."""
        if self.repair_id:
            # Update existing record
            query = """
                UPDATE repairs
                SET equipment_id = %s,
                    repair_type = %s,
                    description = %s,
                    repair_date = %s
                WHERE repair_id = %s
                RETURNING repair_id
            """
            params = (
                self.equipment_id,
                self.repair_type,
                self.description,
                self.repair_date,
                self.repair_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['repair_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO repairs (
                    equipment_id,
                    repair_type,
                    description,
                    repair_date
                ) VALUES (%s, %s, %s, %s)
                RETURNING repair_id
            """
            params = (
                self.equipment_id,
                self.repair_type,
                self.description,
                self.repair_date
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.repair_id = result['repair_id']
                return self.repair_id
            return None
    
    @staticmethod
    def delete(repair_id):
        """Delete repair by ID."""
        query = """
            DELETE FROM repairs
            WHERE repair_id = %s
            RETURNING repair_id
        """
        result = database.execute_query(query, (repair_id,), fetchall=False)
        return result['repair_id'] if result else None

    @staticmethod
    def delete_all():
        """Delete all repairs records."""
        database.execute_query("DELETE FROM repairs")

class DiscardCriteria:
    """DiscardCriteria model representing the discard_criteria table."""
    
    def __init__(self, discard_criteria_id=None, equipment_id=None,
                 criteria_years=0, criteria_kms=0):
        """Initialize a DiscardCriteria instance."""
        self.discard_criteria_id = discard_criteria_id
        self.equipment_id = equipment_id
        self.criteria_years = criteria_years
        self.criteria_kms = criteria_kms
    
    @staticmethod
    def get_all():
        """Get all discard criteria records."""
        query = """
            SELECT dc.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms, e.date_of_induction
            FROM discard_criteria dc
            JOIN equipment e ON dc.equipment_id = e.equipment_id
            ORDER BY e.ba_number, e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_equipment(equipment_id):
        """Get discard criteria for a specific equipment."""
        query = """
            SELECT dc.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms, e.date_of_induction
            FROM discard_criteria dc
            JOIN equipment e ON dc.equipment_id = e.equipment_id
            WHERE dc.equipment_id = %s
        """
        return database.execute_query(query, (equipment_id,))
    
    @staticmethod
    def get_by_id(discard_criteria_id):
        """Get discard criteria by ID."""
        query = """
            SELECT dc.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms, e.date_of_induction
            FROM discard_criteria dc
            JOIN equipment e ON dc.equipment_id = e.equipment_id
            WHERE dc.discard_criteria_id = %s
        """
        return database.execute_query(query, (discard_criteria_id,), fetchall=False)
    
    @staticmethod
    def get_equipment_due_for_discard():
        """Get equipment that is due for discard based on criteria."""
        query = """
            SELECT dc.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms, e.date_of_induction
            FROM discard_criteria dc
            JOIN equipment e ON dc.equipment_id = e.equipment_id
            WHERE (dc.criteria_years > 0 AND e.vintage_years >= dc.criteria_years)
               AND (dc.criteria_kms > 0 AND e.meterage_kms >= dc.criteria_kms)
            ORDER BY e.ba_number, e.make_and_type
        """
        return database.execute_query(query)
    
    def save(self):
        """Save discard criteria to database (insert or update)."""
        if self.discard_criteria_id:
            # Update existing record
            query = """
                UPDATE discard_criteria
                SET equipment_id = %s,
                    criteria_years = %s,
                    criteria_kms = %s
                WHERE discard_criteria_id = %s
                RETURNING discard_criteria_id
            """
            params = (
                self.equipment_id,
                self.criteria_years,
                self.criteria_kms,
                self.discard_criteria_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['discard_criteria_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO discard_criteria (
                    equipment_id,
                    criteria_years,
                    criteria_kms
                ) VALUES (%s, %s, %s)
                RETURNING discard_criteria_id
            """
            params = (
                self.equipment_id,
                self.criteria_years,
                self.criteria_kms
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.discard_criteria_id = result['discard_criteria_id']
                return self.discard_criteria_id
            return None
    
    @staticmethod
    def delete(discard_criteria_id):
        """Delete discard criteria by ID."""
        query = """
            DELETE FROM discard_criteria
            WHERE discard_criteria_id = %s
            RETURNING discard_criteria_id
        """
        result = database.execute_query(query, (discard_criteria_id,), fetchall=False)
        return result['discard_criteria_id'] if result else None

    @staticmethod
    def delete_all():
        """Delete all discard criteria records."""
        query = "DELETE FROM discard_criteria"
        database.execute_query(query)

class TyreMaintenance:
    """TyreMaintenance model representing the tyre_maintenance table."""
    
    def __init__(self, tyre_maintenance_id=None, equipment_id=None, 
                 tyre_rotation_kms=0, tyre_condition_kms=0,
                 tyre_condition_years=0, last_rotation_date=None, date_of_change=None,
                 quantity=0):
        """Initialize a TyreMaintenance instance."""
        self.tyre_maintenance_id = tyre_maintenance_id
        self.equipment_id = equipment_id
        self.tyre_rotation_kms = tyre_rotation_kms
        self.tyre_condition_kms = tyre_condition_kms
        self.tyre_condition_years = tyre_condition_years
        self.last_rotation_date = last_rotation_date
        self.date_of_change = date_of_change
        self.quantity = quantity
    
    @staticmethod
    def get_all():
        """Get all tyre maintenance records."""
        query = """
            SELECT tm.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms
            FROM tyre_maintenance tm
            JOIN equipment e ON tm.equipment_id = e.equipment_id
            ORDER BY e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_equipment(equipment_id):
        """Get tyre maintenance for a specific equipment."""
        query = """
            SELECT tm.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms
            FROM tyre_maintenance tm
            JOIN equipment e ON tm.equipment_id = e.equipment_id
            WHERE tm.equipment_id = %s
        """
        return database.execute_query(query, (equipment_id,), fetchall=False)
    
    @staticmethod
    def get_due_for_rotation():
        """Get equipment due for tyre rotation."""
        query = """
            SELECT tm.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms
            FROM tyre_maintenance tm
            JOIN equipment e ON tm.equipment_id = e.equipment_id
            WHERE tm.tyre_rotation_kms > 0 AND e.is_active = TRUE
            ORDER BY e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_due_for_condition_check():
        """Get equipment due for tyre condition check."""
        query = """
            SELECT tm.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms
            FROM tyre_maintenance tm
            JOIN equipment e ON tm.equipment_id = e.equipment_id
            WHERE (
                (tm.tyre_condition_kms > 0 AND e.meterage_kms >= tm.tyre_condition_kms)
                OR
                (tm.tyre_condition_years > 0 AND e.vintage_years >= tm.tyre_condition_years)
            )
            AND e.is_active = TRUE
            ORDER BY e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_id(tyre_maintenance_id):
        """Get tyre maintenance by ID."""
        query = """
            SELECT tm.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms
            FROM tyre_maintenance tm
            JOIN equipment e ON tm.equipment_id = e.equipment_id
            WHERE tm.tyre_maintenance_id = %s
        """
        return database.execute_query(query, (tyre_maintenance_id,), fetchall=False)
    
    def save(self):
        """Save tyre maintenance to database (insert or update)."""
        if self.tyre_maintenance_id:
            # Update existing record
            query = """
                UPDATE tyre_maintenance
                SET equipment_id = %s,
                    tyre_rotation_kms = %s,
                    tyre_condition_kms = %s,
                    tyre_condition_years = %s,
                    last_rotation_date = %s,
                    date_of_change = %s,
                    quantity = %s
                WHERE tyre_maintenance_id = %s
                RETURNING tyre_maintenance_id
            """
            params = (
                self.equipment_id,
                self.tyre_rotation_kms,
                self.tyre_condition_kms,
                self.tyre_condition_years,
                self.last_rotation_date,
                self.date_of_change,
                self.quantity,
                self.tyre_maintenance_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['tyre_maintenance_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO tyre_maintenance (
                    equipment_id,
                    tyre_rotation_kms,
                    tyre_condition_kms,
                    tyre_condition_years,
                    last_rotation_date,
                    date_of_change,
                    quantity
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING tyre_maintenance_id
            """
            params = (
                self.equipment_id,
                self.tyre_rotation_kms,
                self.tyre_condition_kms,
                self.tyre_condition_years,
                self.last_rotation_date,
                self.date_of_change,
                self.quantity
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.tyre_maintenance_id = result['tyre_maintenance_id']
                return self.tyre_maintenance_id
            return None
    
    @staticmethod
    def delete(tyre_maintenance_id):
        """Delete tyre maintenance by ID."""
        query = """
            DELETE FROM tyre_maintenance
            WHERE tyre_maintenance_id = %s
            RETURNING tyre_maintenance_id
        """
        result = database.execute_query(query, (tyre_maintenance_id,), fetchall=False)
        return result['tyre_maintenance_id'] if result else None

    @staticmethod
    def delete_all():
        """Delete all tyre maintenance records."""
        database.execute_query("DELETE FROM tyre_maintenance")

class DemandForecast:
    """DemandForecast model representing the demand_forecast table."""
    
    def __init__(self, demand_id=None, fluid_id=None, fiscal_year=None,
                 total_requirement=0.0, remarks=None):
        """Initialize a DemandForecast instance."""
        self.demand_id = demand_id
        self.fluid_id = fluid_id
        self.fiscal_year = fiscal_year
        self.total_requirement = total_requirement
        self.remarks = remarks
    
    @staticmethod
    def get_all():
        """Get all demand forecast records."""
        query = """
            SELECT df.*, f.fluid_type, f.sub_type, f.accounting_unit, e.make_and_type, e.ba_number
            FROM demand_forecast df
            JOIN fluids f ON df.fluid_id = f.fluid_id
            JOIN equipment e ON f.equipment_id = e.equipment_id
            ORDER BY df.fiscal_year, e.make_and_type, f.fluid_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_fiscal_year(fiscal_year):
        """Get demand forecasts for a specific fiscal year."""
        query = """
            SELECT df.*, f.fluid_type, f.sub_type, f.accounting_unit, e.make_and_type, e.ba_number
            FROM demand_forecast df
            JOIN fluids f ON df.fluid_id = f.fluid_id
            JOIN equipment e ON f.equipment_id = e.equipment_id
            WHERE df.fiscal_year = %s
            ORDER BY e.make_and_type, f.fluid_type
        """
        return database.execute_query(query, (fiscal_year,))
    
    @staticmethod
    def get_by_id(demand_id):
        """Get demand forecast by ID."""
        query = """
            SELECT df.*, f.fluid_type, f.sub_type, f.accounting_unit, e.make_and_type, e.ba_number
            FROM demand_forecast df
            JOIN fluids f ON df.fluid_id = f.fluid_id
            JOIN equipment e ON f.equipment_id = e.equipment_id
            WHERE df.demand_id = %s
        """
        return database.execute_query(query, (demand_id,), fetchall=False)
    
    @staticmethod
    def get_fiscal_years():
        """Get list of all fiscal years in the system."""
        query = """
            SELECT DISTINCT fiscal_year
            FROM demand_forecast
            ORDER BY fiscal_year
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_summary_by_fiscal_year(fiscal_year):
        """Get summary of demand by fluid type for a specific fiscal year."""
        query = """
            SELECT f.fluid_type, f.accounting_unit, SUM(df.total_requirement) as total
            FROM demand_forecast df
            JOIN fluids f ON df.fluid_id = f.fluid_id
            WHERE df.fiscal_year = %s
            GROUP BY f.fluid_type, f.accounting_unit
            ORDER BY f.fluid_type
        """
        return database.execute_query(query, (fiscal_year,))
    
    def save(self):
        """Save demand forecast to database (insert or update)."""
        if self.demand_id:
            # Update existing record
            query = """
                UPDATE demand_forecast
                SET fluid_id = %s,
                    fiscal_year = %s,
                    total_requirement = %s,
                    remarks = %s
                WHERE demand_id = %s
                RETURNING demand_id
            """
            params = (
                self.fluid_id,
                self.fiscal_year,
                self.total_requirement,
                self.remarks,
                self.demand_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['demand_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO demand_forecast (
                    fluid_id,
                    fiscal_year,
                    total_requirement,
                    remarks
                ) VALUES (%s, %s, %s, %s)
                RETURNING demand_id
            """
            params = (
                self.fluid_id,
                self.fiscal_year,
                self.total_requirement,
                self.remarks
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.demand_id = result['demand_id']
                return self.demand_id
            return None
    
    @staticmethod
    def delete(demand_id):
        """Delete demand forecast by ID."""
        query = """
            DELETE FROM demand_forecast
            WHERE demand_id = %s
            RETURNING demand_id
        """
        result = database.execute_query(query, (demand_id,), fetchall=False)
        return result['demand_id'] if result else None


class TyreForecast:
    """TyreForecast model representing the tyre_forecast table."""
    
    def __init__(self, forecast_id=None, equipment_id=None, fiscal_year=None,
                 tyre_type=None, quantity_required=0, total_requirement=0.0, remarks=None):
        """Initialize a TyreForecast instance."""
        self.forecast_id = forecast_id
        self.equipment_id = equipment_id
        self.fiscal_year = fiscal_year
        self.tyre_type = tyre_type
        self.quantity_required = quantity_required
        self.total_requirement = total_requirement
        self.remarks = remarks
    
    @staticmethod
    def get_all():
        """Get all tyre forecast records."""
        query = """
            SELECT tf.*, e.make_and_type, e.ba_number, e.serial_number
            FROM tyre_forecast tf
            JOIN equipment e ON tf.equipment_id = e.equipment_id
            ORDER BY tf.fiscal_year, e.make_and_type, tf.tyre_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_fiscal_year(fiscal_year):
        """Get tyre forecasts for a specific fiscal year."""
        query = """
            SELECT tf.*, e.make_and_type, e.ba_number, e.serial_number
            FROM tyre_forecast tf
            JOIN equipment e ON tf.equipment_id = e.equipment_id
            WHERE tf.fiscal_year = %s
            ORDER BY e.make_and_type, tf.tyre_type
        """
        return database.execute_query(query, (fiscal_year,))
    
    @staticmethod
    def get_by_id(forecast_id):
        """Get tyre forecast by ID."""
        query = """
            SELECT tf.*, e.make_and_type, e.ba_number, e.serial_number
            FROM tyre_forecast tf
            JOIN equipment e ON tf.equipment_id = e.equipment_id
            WHERE tf.forecast_id = %s
        """
        return database.execute_query(query, (forecast_id,), fetchall=False)
    
    @staticmethod
    def get_fiscal_years():
        """Get list of all fiscal years in tyre forecasts."""
        query = """
            SELECT DISTINCT fiscal_year
            FROM tyre_forecast
            ORDER BY fiscal_year
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_summary_by_fiscal_year(fiscal_year):
        """Get summary of tyre demand by type for a specific fiscal year."""
        query = """
            SELECT tf.tyre_type, SUM(tf.total_requirement) as total
            FROM tyre_forecast tf
            WHERE tf.fiscal_year = %s
            GROUP BY tf.tyre_type
            ORDER BY tf.tyre_type
        """
        return database.execute_query(query, (fiscal_year,))
    
    def save(self):
        """Save tyre forecast to database (insert or update)."""
        if self.forecast_id:
            # Update existing record
            query = """
                UPDATE tyre_forecast
                SET equipment_id = %s,
                    fiscal_year = %s,
                    tyre_type = %s,
                    quantity_required = %s,
                    total_requirement = %s,
                    remarks = %s,
                    updated_at = datetime('now')
                WHERE forecast_id = %s
                RETURNING forecast_id
            """
            params = (
                self.equipment_id,
                self.fiscal_year,
                self.tyre_type,
                self.quantity_required,
                self.total_requirement,
                self.remarks,
                self.forecast_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['forecast_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO tyre_forecast (
                    equipment_id,
                    fiscal_year,
                    tyre_type,
                    quantity_required,
                    total_requirement,
                    remarks
                ) VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING forecast_id
            """
            params = (
                self.equipment_id,
                self.fiscal_year,
                self.tyre_type,
                self.quantity_required,
                self.total_requirement,
                self.remarks
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.forecast_id = result['forecast_id']
                return self.forecast_id
            return None
    
    @staticmethod
    def delete(forecast_id):
        """Delete tyre forecast by ID."""
        query = """
            DELETE FROM tyre_forecast
            WHERE forecast_id = %s
            RETURNING forecast_id
        """
        result = database.execute_query(query, (forecast_id,), fetchall=False)
        return result['forecast_id'] if result else None


class BatteryForecast:
    """BatteryForecast model representing the battery_forecast table."""
    
    def __init__(self, forecast_id=None, equipment_id=None, fiscal_year=None,
                 battery_type=None, voltage=0.0, quantity_required=0, total_requirement=0.0, remarks=None):
        """Initialize a BatteryForecast instance."""
        self.forecast_id = forecast_id
        self.equipment_id = equipment_id
        self.fiscal_year = fiscal_year
        self.battery_type = battery_type
        self.voltage = voltage
        self.quantity_required = quantity_required
        self.total_requirement = total_requirement
        self.remarks = remarks
    
    @staticmethod
    def get_all():
        """Get all battery forecast records."""
        query = """
            SELECT bf.*, e.make_and_type, e.ba_number, e.serial_number
            FROM battery_forecast bf
            JOIN equipment e ON bf.equipment_id = e.equipment_id
            ORDER BY bf.fiscal_year, e.make_and_type, bf.battery_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_fiscal_year(fiscal_year):
        """Get battery forecasts for a specific fiscal year."""
        query = """
            SELECT bf.*, e.make_and_type, e.ba_number, e.serial_number
            FROM battery_forecast bf
            JOIN equipment e ON bf.equipment_id = e.equipment_id
            WHERE bf.fiscal_year = %s
            ORDER BY e.make_and_type, bf.battery_type
        """
        return database.execute_query(query, (fiscal_year,))
    
    @staticmethod
    def get_by_id(forecast_id):
        """Get battery forecast by ID."""
        query = """
            SELECT bf.*, e.make_and_type, e.ba_number, e.serial_number
            FROM battery_forecast bf
            JOIN equipment e ON bf.equipment_id = e.equipment_id
            WHERE bf.forecast_id = %s
        """
        return database.execute_query(query, (forecast_id,), fetchall=False)
    
    @staticmethod
    def get_fiscal_years():
        """Get list of all fiscal years in battery forecasts."""
        query = """
            SELECT DISTINCT fiscal_year
            FROM battery_forecast
            ORDER BY fiscal_year
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_summary_by_fiscal_year(fiscal_year):
        """Get summary of battery demand by type for a specific fiscal year."""
        query = """
            SELECT bf.battery_type, bf.voltage, SUM(bf.total_requirement) as total
            FROM battery_forecast bf
            WHERE bf.fiscal_year = %s
            GROUP BY bf.battery_type, bf.voltage
            ORDER BY bf.battery_type, bf.voltage
        """
        return database.execute_query(query, (fiscal_year,))
    
    def save(self):
        """Save battery forecast to database (insert or update)."""
        if self.forecast_id:
            # Update existing record
            query = """
                UPDATE battery_forecast
                SET equipment_id = %s,
                    fiscal_year = %s,
                    battery_type = %s,
                    voltage = %s,
                    quantity_required = %s,
                    total_requirement = %s,
                    remarks = %s,
                    updated_at = datetime('now')
                WHERE forecast_id = %s
                RETURNING forecast_id
            """
            params = (
                self.equipment_id,
                self.fiscal_year,
                self.battery_type,
                self.voltage,
                self.quantity_required,
                self.total_requirement,
                self.remarks,
                self.forecast_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['forecast_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO battery_forecast (
                    equipment_id,
                    fiscal_year,
                    battery_type,
                    voltage,
                    quantity_required,
                    total_requirement,
                    remarks
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING forecast_id
            """
            params = (
                self.equipment_id,
                self.fiscal_year,
                self.battery_type,
                self.voltage,
                self.quantity_required,
                self.total_requirement,
                self.remarks
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.forecast_id = result['forecast_id']
                return self.forecast_id
            return None
    
    @staticmethod
    def delete(forecast_id):
        """Delete battery forecast by ID."""
        query = """
            DELETE FROM battery_forecast
            WHERE forecast_id = %s
            RETURNING forecast_id
        """
        result = database.execute_query(query, (forecast_id,), fetchall=False)
        return result['forecast_id'] if result else None


class EquipmentForecast:
    """EquipmentForecast model representing the equipment_forecast table."""
    
    def __init__(self, forecast_id=None, equipment_id=None, fiscal_year=None,
                 equipment_type=None, replacement_reason=None, quantity_required=1, 
                 total_requirement=0.0, remarks=None):
        """Initialize an EquipmentForecast instance."""
        self.forecast_id = forecast_id
        self.equipment_id = equipment_id
        self.fiscal_year = fiscal_year
        self.equipment_type = equipment_type
        self.replacement_reason = replacement_reason
        self.quantity_required = quantity_required
        self.total_requirement = total_requirement
        self.remarks = remarks
    
    @staticmethod
    def get_all():
        """Get all equipment forecast records."""
        query = """
            SELECT ef.*, e.make_and_type, e.ba_number, e.serial_number
            FROM equipment_forecast ef
            JOIN equipment e ON ef.equipment_id = e.equipment_id
            ORDER BY ef.fiscal_year, e.make_and_type, ef.equipment_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_fiscal_year(fiscal_year):
        """Get equipment forecasts for a specific fiscal year."""
        query = """
            SELECT ef.*, e.make_and_type, e.ba_number, e.serial_number
            FROM equipment_forecast ef
            JOIN equipment e ON ef.equipment_id = e.equipment_id
            WHERE ef.fiscal_year = %s
            ORDER BY e.make_and_type, ef.equipment_type
        """
        return database.execute_query(query, (fiscal_year,))
    
    @staticmethod
    def get_by_id(forecast_id):
        """Get equipment forecast by ID."""
        query = """
            SELECT ef.*, e.make_and_type, e.ba_number, e.serial_number
            FROM equipment_forecast ef
            JOIN equipment e ON ef.equipment_id = e.equipment_id
            WHERE ef.forecast_id = %s
        """
        return database.execute_query(query, (forecast_id,), fetchall=False)
    
    @staticmethod
    def get_fiscal_years():
        """Get list of all fiscal years in equipment forecasts."""
        query = """
            SELECT DISTINCT fiscal_year
            FROM equipment_forecast
            ORDER BY fiscal_year
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_summary_by_fiscal_year(fiscal_year):
        """Get summary of equipment demand by type for a specific fiscal year."""
        query = """
            SELECT ef.equipment_type, SUM(ef.total_requirement) as total
            FROM equipment_forecast ef
            WHERE ef.fiscal_year = %s
            GROUP BY ef.equipment_type
            ORDER BY ef.equipment_type
        """
        return database.execute_query(query, (fiscal_year,))
    
    def save(self):
        """Save equipment forecast to database (insert or update)."""
        if self.forecast_id:
            # Update existing record
            query = """
                UPDATE equipment_forecast
                SET equipment_id = %s,
                    fiscal_year = %s,
                    equipment_type = %s,
                    replacement_reason = %s,
                    quantity_required = %s,
                    total_requirement = %s,
                    remarks = %s,
                    updated_at = datetime('now')
                WHERE forecast_id = %s
                RETURNING forecast_id
            """
            params = (
                self.equipment_id,
                self.fiscal_year,
                self.equipment_type,
                self.replacement_reason,
                self.quantity_required,
                self.total_requirement,
                self.remarks,
                self.forecast_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['forecast_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO equipment_forecast (
                    equipment_id,
                    fiscal_year,
                    equipment_type,
                    replacement_reason,
                    quantity_required,
                    total_requirement,
                    remarks
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING forecast_id
            """
            params = (
                self.equipment_id,
                self.fiscal_year,
                self.equipment_type,
                self.replacement_reason,
                self.quantity_required,
                self.total_requirement,
                self.remarks
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.forecast_id = result['forecast_id']
                return self.forecast_id
            return None
    
    @staticmethod
    def delete(forecast_id):
        """Delete equipment forecast by ID."""
        query = """
            DELETE FROM equipment_forecast
            WHERE forecast_id = %s
            RETURNING forecast_id
        """
        result = database.execute_query(query, (forecast_id,), fetchall=False)
        return result['forecast_id'] if result else None

        # --- Overhaul Model ---
class Overhaul:
    def __init__(self, overhaul_id=None, equipment_id=None, overhaul_type=None,
                 overhaul_date=None, done_date=None, due_date=None, status='scheduled',
                 meter_reading=0, hours_reading=0, notes=None, description=None,
                 created_date=None, updated_date=None, completed_by=None, completion_notes=None):
        """Initialize an Overhaul instance."""
        self.overhaul_id = overhaul_id
        self.equipment_id = equipment_id
        self.overhaul_type = overhaul_type
        self.overhaul_date = overhaul_date  # Legacy field
        self.done_date = done_date
        self.due_date = due_date
        self.status = status
        self.meter_reading = meter_reading or 0
        self.hours_reading = hours_reading or 0
        self.notes = notes
        self.description = description
        self.created_date = created_date
        self.updated_date = updated_date
        self.completed_by = completed_by
        self.completion_notes = completion_notes

    @staticmethod
    def get_by_equipment_id(equipment_id):
        """Alias for backward compatibility."""
        return Overhaul.get_by_equipment(equipment_id)

    @staticmethod
    def update(overhaul_id, **kwargs):
        """
        Update an existing overhaul record by ID.
        Accepts keyword arguments for fields to update.
        """
        existing = Overhaul.get_by_id(overhaul_id)
        if not existing:
            return None
        data = dict(existing)
        data.update(kwargs)
        overhaul = Overhaul(**data)
        return overhaul.save()

    @staticmethod
    def create(**kwargs):
        """
        Create a new overhaul record.
        Accepts keyword arguments for all Overhaul fields.
        """
        overhaul = Overhaul(**kwargs)
        return overhaul.save()

    @staticmethod
    def delete_by_id(overhaul_id):
        """Delete an overhaul record by ID."""
        query = "DELETE FROM overhauls WHERE overhaul_id = %s RETURNING overhaul_id"
        result = database.execute_query(query, (overhaul_id,), fetchall=False)
        return result is not None

    @staticmethod
    def delete_all():
        """Delete all overhaul records."""
        query = "DELETE FROM overhauls"
        database.execute_query(query)
        return True



    @staticmethod
    def get_all():
        query = '''
            SELECT o.*, e.make_and_type, e.ba_number
            FROM overhauls o
            JOIN equipment e ON o.equipment_id = e.equipment_id
            ORDER BY o.done_date DESC, o.due_date DESC, e.make_and_type
        '''
        return database.execute_query(query)

    @staticmethod
    def get_by_equipment(equipment_id):
        query = '''
            SELECT o.*, e.make_and_type, e.ba_number
            FROM overhauls o
            JOIN equipment e ON o.equipment_id = e.equipment_id
            WHERE o.equipment_id = %s
            ORDER BY o.overhaul_date DESC
        '''
        return database.execute_query(query, (equipment_id,))

    @staticmethod
    def get_by_id(overhaul_id):
        query = '''
            SELECT o.*, e.make_and_type, e.ba_number
            FROM overhauls o
            JOIN equipment e ON o.equipment_id = e.equipment_id
            WHERE o.overhaul_id = %s
        '''
        return database.execute_query(query, (overhaul_id,), fetchall=False)

    def save(self):
        if self.overhaul_id:
            query = '''
                UPDATE overhauls
                SET equipment_id = %s,
                    overhaul_type = %s,
                    done_date = %s,
                    due_date = %s,
                    status = %s,
                    meter_reading = %s,
                    hours_reading = %s,
                    notes = %s,
                    description = %s,
                    completed_by = %s,
                    completion_notes = %s,
                    updated_date = datetime('now')
                WHERE overhaul_id = %s
                RETURNING overhaul_id
            '''
            params = (
                self.equipment_id,
                self.overhaul_type,
                self.done_date,
                self.due_date,
                self.status,
                self.meter_reading,
                self.hours_reading,
                self.notes,
                self.description,
                self.completed_by,
                self.completion_notes,
                self.overhaul_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['overhaul_id'] if result else None
        else:
            query = '''
                INSERT INTO overhauls (
                    equipment_id,
                    overhaul_type,
                    done_date,
                    due_date,
                    status,
                    meter_reading,
                    hours_reading,
                    notes,
                    description,
                    completed_by,
                    completion_notes
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING overhaul_id
            '''
            params = (
                self.equipment_id,
                self.overhaul_type,
                self.done_date,
                self.due_date,
                self.status,
                self.meter_reading,
                self.hours_reading,
                self.notes,
                self.description,
                self.completed_by,
                self.completion_notes
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.overhaul_id = result['overhaul_id']
                return self.overhaul_id
            return None

    @staticmethod
    def mark_completed(overhaul_id, completion_date, completed_by=None, completion_notes=None, meter_reading=None):
        """Mark an overhaul as completed with validation."""
        try:
            from datetime import date
            import overhaul_service

            # Get the overhaul record
            overhaul = Overhaul.get_by_id(overhaul_id)
            if not overhaul:
                raise ValueError(f"Overhaul not found: {overhaul_id}")

            # Use the service layer for completion logic
            return overhaul_service.complete_overhaul(
                overhaul['equipment_id'],
                overhaul['overhaul_type'],
                completion_date,
                completed_by,
                completion_notes,
                meter_reading
            )

        except Exception as e:
            logger.error(f"Error marking overhaul {overhaul_id} as completed: {e}")
            raise e

    @staticmethod
    def get_equipment_overhaul_status(equipment_id):
        """Get comprehensive overhaul status for equipment."""
        try:
            import overhaul_service
            return overhaul_service.get_overhaul_summary(equipment_id)
        except Exception as e:
            logger.error(f"Error getting overhaul status for equipment {equipment_id}: {e}")
            return None

    @staticmethod
    def delete(overhaul_id):
        query = '''
            DELETE FROM overhauls
            WHERE overhaul_id = %s
            RETURNING overhaul_id
        '''
        result = database.execute_query(query, (overhaul_id,), fetchall=False)
        return result['overhaul_id'] if result else None

    @staticmethod
    def delete_all():
        """Delete all overhaul records."""
        query = "DELETE FROM overhauls"
        database.execute_query(query)

class MediumReset:
    """Medium Reset model representing the medium_resets table."""
    
    def __init__(self, medium_reset_id=None, equipment_id=None, reset_type=None, done_date=None, due_date=None, mr1_due_date=None, oh1_due_date=None, mr2_due_date=None, oh2_due_date=None, discard_due_date=None, status=None, meter_reading=None, hours_reading=None, notes=None, description=None, **kwargs):
        self.medium_reset_id = medium_reset_id
        self.equipment_id = equipment_id
        self.reset_type = reset_type
        self.done_date = done_date
        self.due_date = due_date
        self.mr1_due_date = mr1_due_date
        self.oh1_due_date = oh1_due_date
        self.mr2_due_date = mr2_due_date
        self.oh2_due_date = oh2_due_date
        self.discard_due_date = discard_due_date
        self.status = status
        self.meter_reading = meter_reading
        self.hours_reading = hours_reading
        self.notes = notes
        self.description = description
        # Ignore any extra keyword arguments for robustness

    @staticmethod
    def get_all():
        query = '''
            SELECT mr.*, e.make_and_type, e.ba_number
            FROM medium_resets mr
            JOIN equipment e ON mr.equipment_id = e.equipment_id
            WHERE e.make_and_type IN ('ICV BMP-I', 'ICV BMP-II', 'AERV')
            ORDER BY mr.done_date DESC, mr.due_date DESC, e.make_and_type
        '''
        return database.execute_query(query)

    @staticmethod
    def get_by_equipment(equipment_id):
        query = '''
            SELECT mr.*, e.make_and_type, e.ba_number
            FROM medium_resets mr
            JOIN equipment e ON mr.equipment_id = e.equipment_id
            WHERE mr.equipment_id = %s
            ORDER BY mr.done_date DESC
        '''
        return database.execute_query(query, (equipment_id,))

    @staticmethod
    def get_by_id(medium_reset_id):
        query = '''
            SELECT mr.*, e.make_and_type, e.ba_number
            FROM medium_resets mr
            JOIN equipment e ON mr.equipment_id = e.equipment_id
            WHERE mr.medium_reset_id = %s
        '''
        return database.execute_query(query, (medium_reset_id,), fetchall=False)

    def save(self):
        if self.medium_reset_id:
            query = '''
                UPDATE medium_resets
                SET equipment_id = %s,
                    reset_type = %s,
                    done_date = %s,
                    due_date = %s,
                    mr1_due_date = %s,
                    oh1_due_date = %s,
                    mr2_due_date = %s,
                    oh2_due_date = %s,
                    discard_due_date = %s,
                    status = %s,
                    meter_reading = %s,
                    hours_reading = %s,
                    notes = %s,
                    description = %s
                WHERE medium_reset_id = %s
                RETURNING medium_reset_id
            '''
            params = (
                self.equipment_id,
                self.reset_type,
                self.done_date,
                self.due_date,
                self.mr1_due_date,
                self.oh1_due_date,
                self.mr2_due_date,
                self.oh2_due_date,
                self.discard_due_date,
                self.status,
                self.meter_reading,
                self.hours_reading,
                self.notes,
                self.description,
                self.medium_reset_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['medium_reset_id'] if result else None
        else:
            query = '''
                INSERT INTO medium_resets (
                    equipment_id,
                    reset_type,
                    done_date,
                    due_date,
                    mr1_due_date,
                    oh1_due_date,
                    mr2_due_date,
                    oh2_due_date,
                    discard_due_date,
                    status,
                    meter_reading,
                    hours_reading,
                    notes,
                    description
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING medium_reset_id
            '''
            params = (
                self.equipment_id,
                self.reset_type,
                self.done_date,
                self.due_date,
                self.mr1_due_date,
                self.oh1_due_date,
                self.mr2_due_date,
                self.oh2_due_date,
                self.discard_due_date,
                self.status,
                self.meter_reading,
                self.hours_reading,
                self.notes,
                self.description
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.medium_reset_id = result['medium_reset_id']
                return self.medium_reset_id
            return None

    @staticmethod
    def delete(medium_reset_id):
        query = '''
            DELETE FROM medium_resets
            WHERE medium_reset_id = %s
            RETURNING medium_reset_id
        '''
        result = database.execute_query(query, (medium_reset_id,), fetchall=False)
        return result['medium_reset_id'] if result else None

    @staticmethod
    def delete_all():
        """Delete all medium reset records."""
        query = "DELETE FROM medium_resets"
        database.execute_query(query)


class OverhaulForecast:
    """OverhaulForecast model representing the overhaul_forecast table."""
    
    def __init__(self, forecast_id=None, equipment_id=None, fiscal_year=None,
                 overhaul_type=None, total_requirement=0.0, remarks=None):
        """Initialize an OverhaulForecast instance."""
        self.forecast_id = forecast_id
        self.equipment_id = equipment_id
        self.fiscal_year = fiscal_year
        self.overhaul_type = overhaul_type
        self.total_requirement = total_requirement
        self.remarks = remarks
    
    @staticmethod
    def get_all():
        """Get all overhaul forecast records."""
        query = """
            SELECT of.*, e.make_and_type, e.ba_number, e.serial_number
            FROM overhaul_forecast of
            JOIN equipment e ON of.equipment_id = e.equipment_id
            ORDER BY of.fiscal_year, e.make_and_type, of.overhaul_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_fiscal_year(fiscal_year):
        """Get overhaul forecasts for a specific fiscal year."""
        query = """
            SELECT of.*, e.make_and_type, e.ba_number, e.serial_number
            FROM overhaul_forecast of
            JOIN equipment e ON of.equipment_id = e.equipment_id
            WHERE of.fiscal_year = %s
            ORDER BY e.make_and_type, of.overhaul_type
        """
        return database.execute_query(query, (fiscal_year,))
    
    @staticmethod
    def get_by_id(forecast_id):
        """Get overhaul forecast by ID."""
        query = """
            SELECT of.*, e.make_and_type, e.ba_number, e.serial_number
            FROM overhaul_forecast of
            JOIN equipment e ON of.equipment_id = e.equipment_id
            WHERE of.forecast_id = %s
        """
        return database.execute_query(query, (forecast_id,), fetchall=False)
    
    def save(self):
        """Save overhaul forecast to database (insert or update)."""
        if self.forecast_id:
            # Update existing record
            query = """
                UPDATE overhaul_forecast
                SET equipment_id = %s,
                    fiscal_year = %s,
                    overhaul_type = %s,
                    total_requirement = %s,
                    remarks = %s
                WHERE forecast_id = %s
                RETURNING forecast_id
            """
            params = (
                self.equipment_id,
                self.fiscal_year,
                self.overhaul_type,
                self.total_requirement,
                self.remarks,
                self.forecast_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['forecast_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO overhaul_forecast (
                    equipment_id,
                    fiscal_year,
                    overhaul_type,
                    total_requirement,
                    remarks
                ) VALUES (%s, %s, %s, %s, %s)
                RETURNING forecast_id
            """
            params = (
                self.equipment_id,
                self.fiscal_year,
                self.overhaul_type,
                self.total_requirement,
                self.remarks
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.forecast_id = result['forecast_id']
                return self.forecast_id
            return None
    
    @staticmethod
    def delete(forecast_id):
        """Delete overhaul forecast by ID."""
        query = """
            DELETE FROM overhaul_forecast
            WHERE forecast_id = %s
            RETURNING forecast_id
        """
        result = database.execute_query(query, (forecast_id,), fetchall=False)
        return result['forecast_id'] if result else None

class ConditioningForecast:
    """ConditioningForecast model representing the conditioning_forecast table."""
    
    def __init__(self, forecast_id=None, equipment_id=None, fiscal_year=None,
                 conditioning_type=None, total_requirement=0.0, remarks=None):
        """Initialize a ConditioningForecast instance."""
        self.forecast_id = forecast_id
        self.equipment_id = equipment_id
        self.fiscal_year = fiscal_year
        self.conditioning_type = conditioning_type
        self.total_requirement = total_requirement
        self.remarks = remarks
    
    @staticmethod
    def get_all():
        """Get all conditioning forecast records."""
        query = """
            SELECT cf.*, e.make_and_type, e.ba_number, e.serial_number
            FROM conditioning_forecast cf
            JOIN equipment e ON cf.equipment_id = e.equipment_id
            ORDER BY cf.fiscal_year, e.make_and_type, cf.conditioning_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_fiscal_year(fiscal_year):
        """Get conditioning forecasts for a specific fiscal year."""
        query = """
            SELECT cf.*, e.make_and_type, e.ba_number, e.serial_number
            FROM conditioning_forecast cf
            JOIN equipment e ON cf.equipment_id = e.equipment_id
            WHERE cf.fiscal_year = %s
            ORDER BY e.make_and_type, cf.conditioning_type
        """
        return database.execute_query(query, (fiscal_year,))
    
    @staticmethod
    def get_by_id(forecast_id):
        """Get conditioning forecast by ID."""
        query = """
            SELECT cf.*, e.make_and_type, e.ba_number, e.serial_number
            FROM conditioning_forecast cf
            JOIN equipment e ON cf.equipment_id = e.equipment_id
            WHERE cf.forecast_id = %s
        """
        return database.execute_query(query, (forecast_id,), fetchall=False)
    
    def save(self):
        """Save conditioning forecast to database (insert or update)."""
        if self.forecast_id:
            # Update existing record
            query = """
                UPDATE conditioning_forecast
                SET equipment_id = %s,
                    fiscal_year = %s,
                    conditioning_type = %s,
                    total_requirement = %s,
                    remarks = %s
                WHERE forecast_id = %s
                RETURNING forecast_id
            """
            params = (
                self.equipment_id,
                self.fiscal_year,
                self.conditioning_type,
                self.total_requirement,
                self.remarks,
                self.forecast_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['forecast_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO conditioning_forecast (
                    equipment_id,
                    fiscal_year,
                    conditioning_type,
                    total_requirement,
                    remarks
                ) VALUES (%s, %s, %s, %s, %s)
                RETURNING forecast_id
            """
            params = (
                self.equipment_id,
                self.fiscal_year,
                self.conditioning_type,
                self.total_requirement,
                self.remarks
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.forecast_id = result['forecast_id']
                return self.forecast_id
            return None
    
    @staticmethod
    def delete(forecast_id):
        """Delete conditioning forecast by ID."""
        query = """
            DELETE FROM conditioning_forecast
            WHERE forecast_id = %s
            RETURNING forecast_id
        """
        result = database.execute_query(query, (forecast_id,), fetchall=False)
        return result['forecast_id'] if result else None

class Battery:
    """Battery model representing the battery table."""
    
    def __init__(self, battery_id=None, equipment_id=None, done_date=None, 
                 custom_life_months=None):
        """Initialize a Battery instance."""
        self.battery_id = battery_id
        self.equipment_id = equipment_id
        self.done_date = done_date
        self.custom_life_months = custom_life_months  # If None, defaults to 24 months (2 years)
    
    @staticmethod
    def get_all():
        """Get all battery records with equipment information."""
        query = """
            SELECT b.*, e.make_and_type, e.ba_number
            FROM battery b
            JOIN equipment e ON b.equipment_id = e.equipment_id
            ORDER BY e.make_and_type
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_equipment(equipment_id):
        """Get battery record for a specific equipment."""
        query = """
            SELECT b.*, e.make_and_type, e.ba_number
            FROM battery b
            JOIN equipment e ON b.equipment_id = e.equipment_id
            WHERE b.equipment_id = %s
        """
        return database.execute_query(query, (equipment_id,), fetchall=False)
    
    @staticmethod
    def get_by_id(battery_id):
        """Get battery by ID."""
        query = """
            SELECT b.*, e.make_and_type, e.ba_number
            FROM battery b
            JOIN equipment e ON b.equipment_id = e.equipment_id
            WHERE b.battery_id = %s
        """
        return database.execute_query(query, (battery_id,), fetchall=False)
    
    def save(self):
        """Save battery to database (insert or update)."""
        if self.battery_id:
            # Update existing record
            query = """
                UPDATE battery
                SET equipment_id = %s,
                    done_date = %s,
                    custom_life_months = %s
                WHERE battery_id = %s
                RETURNING battery_id
            """
            params = (
                self.equipment_id,
                self.done_date,
                self.custom_life_months,
                self.battery_id
            )
            result = database.execute_query(query, params, fetchall=False)
            return result['battery_id'] if result else None
        else:
            # Insert new record
            query = """
                INSERT INTO battery (
                    equipment_id,
                    done_date,
                    custom_life_months
                ) VALUES (%s, %s, %s)
                RETURNING battery_id
            """
            params = (
                self.equipment_id,
                self.done_date,
                self.custom_life_months
            )
            result = database.execute_query(query, params, fetchall=False)
            if result:
                self.battery_id = result['battery_id']
                return self.battery_id
            return None
    
    @staticmethod
    def delete(battery_id):
        """Delete battery by ID."""
        query = """
            DELETE FROM battery
            WHERE battery_id = %s
            RETURNING battery_id
        """
        result = database.execute_query(query, (battery_id,), fetchall=False)
        return result['battery_id'] if result else None
    
    @staticmethod
    def delete_all():
        """Delete all battery records."""
        database.execute_query("DELETE FROM battery")


class MaintenanceArchive:
    """Maintenance archive model for storing historical maintenance records."""
    
    def __init__(self, archive_id=None, archive_name=None, archive_type=None,
                 maintenance_category=None, period_start=None, period_end=None,
                 created_date=None, created_by=None, record_count=0,
                 archive_data=None, notes=None):
        """Initialize a MaintenanceArchive instance."""
        self.archive_id = archive_id
        self.archive_name = archive_name
        self.archive_type = archive_type  # 'monthly', 'yearly', 'custom'
        self.maintenance_category = maintenance_category
        self.period_start = period_start
        self.period_end = period_end
        self.created_date = created_date
        self.created_by = created_by
        self.record_count = record_count
        self.archive_data = archive_data  # JSON string with archived records
        self.notes = notes
    
    @staticmethod
    def get_all():
        """Get all maintenance archives ordered by creation date (newest first)."""
        query = """
            SELECT * FROM maintenance_archives
            ORDER BY created_date DESC
        """
        return database.execute_query(query)
    
    @staticmethod
    def get_by_category(category):
        """Get archives by maintenance category."""
        query = """
            SELECT * FROM maintenance_archives
            WHERE maintenance_category = %s
            ORDER BY created_date DESC
        """
        return database.execute_query(query, (category,))
    
    @staticmethod
    def get_by_type(archive_type):
        """Get archives by type (monthly, yearly, custom)."""
        query = """
            SELECT * FROM maintenance_archives
            WHERE archive_type = %s
            ORDER BY created_date DESC
        """
        return database.execute_query(query, (archive_type,))
    
    @staticmethod
    def get_by_year(year):
        """Get archives for a specific year."""
        query = """
            SELECT * FROM maintenance_archives
            WHERE strftime('%Y', period_start) = %s 
            OR strftime('%Y', period_end) = %s
            ORDER BY period_start DESC
        """
        return database.execute_query(query, (str(year), str(year)))
    
    @staticmethod
    def get_by_year_and_category(year, category):
        """Get archives for a specific year and category."""
        query = """
            SELECT * FROM maintenance_archives
            WHERE (strftime('%Y', period_start) = %s OR strftime('%Y', period_end) = %s)
            AND maintenance_category = %s
            ORDER BY period_start DESC
        """
        return database.execute_query(query, (str(year), str(year), category))
    
    @staticmethod
    def get_by_id(archive_id):
        """Get archive by ID."""
        query = """
            SELECT * FROM maintenance_archives
            WHERE archive_id = %s
        """
        result = database.execute_query(query, (archive_id,), fetchall=False)
        return result
    
    def save(self):
        """Save the maintenance archive to database."""
        if self.archive_id:
            # Update existing record
            query = """
                UPDATE maintenance_archives SET
                    archive_name = %s,
                    archive_type = %s,
                    maintenance_category = %s,
                    period_start = %s,
                    period_end = %s,
                    created_by = %s,
                    record_count = %s,
                    archive_data = %s,
                    notes = %s
                WHERE archive_id = %s
                RETURNING archive_id
            """
            params = (
                self.archive_name,
                self.archive_type,
                self.maintenance_category,
                self.period_start,
                self.period_end,
                self.created_by,
                self.record_count,
                self.archive_data,
                self.notes,
                self.archive_id
            )
        else:
            # Insert new record
            query = """
                INSERT INTO maintenance_archives (
                    archive_name,
                    archive_type,
                    maintenance_category,
                    period_start,
                    period_end,
                    created_by,
                    record_count,
                    archive_data,
                    notes
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING archive_id
            """
            params = (
                self.archive_name,
                self.archive_type,
                self.maintenance_category,
                self.period_start,
                self.period_end,
                self.created_by,
                self.record_count,
                self.archive_data,
                self.notes
            )
        
        result = database.execute_query(query, params, fetchall=False)
        if result:
            self.archive_id = result['archive_id']
            return self.archive_id
        return None
    
    @staticmethod
    def delete(archive_id):
        """Delete archive by ID."""
        query = """
            DELETE FROM maintenance_archives
            WHERE archive_id = %s
            RETURNING archive_id
        """
        result = database.execute_query(query, (archive_id,), fetchall=False)
        return result['archive_id'] if result else None
    
    @staticmethod
    def delete_all():
        """Delete all archive records."""
        database.execute_query("DELETE FROM maintenance_archives")
    
    def get_archived_records(self):
        """Get the archived maintenance records as a list of dictionaries."""
        if not self.archive_data:
            return []
        
        try:
            import json
            return json.loads(self.archive_data)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_archived_records(self, records):
        """Set the archived maintenance records from a list of dictionaries."""
        try:
            import json
            self.archive_data = json.dumps(records, default=str)
            self.record_count = len(records)
        except (TypeError, ValueError):
            self.archive_data = None
            self.record_count = 0

# Create tables
database.execute_query('''
    CREATE TABLE IF NOT EXISTS equipment (
        equipment_id INTEGER PRIMARY KEY AUTOINCREMENT,
        serial_number TEXT,
        make_and_type TEXT,
        ba_number TEXT,
        units_held INTEGER,
        vintage_years REAL,
        meterage_kms REAL,
        km_hrs_run_previous_month REAL,
        km_hrs_run_current_month REAL,
        is_active BOOLEAN,
        remarks TEXT,
        date_of_commission TEXT  -- Added column
    );
''')

database.execute_query('''
    CREATE TABLE IF NOT EXISTS fluids (
        fluid_id INTEGER PRIMARY KEY AUTOINCREMENT,
        equipment_id INTEGER,
        fluid_type TEXT,
        sub_type TEXT,
        accounting_unit TEXT,
        capacity_ltrs_kg REAL,
        addl_10_percent_top_up REAL,
        top_up_percent REAL DEFAULT NULL,
        grade TEXT,
        periodicity_km INTEGER,
        periodicity_hrs INTEGER,
        periodicity_months INTEGER,
        last_serviced_date DATE,
        last_serviced_meterage REAL,
        FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
    );
''')

# Add top_up_percent column if it does not exist (for migration)
def update_fluids_table_schema():
    try:
        columns = database.execute_query("PRAGMA table_info(fluids)")
        col_names = [col['name'] for col in columns]
        if 'top_up_percent' not in col_names:
            database.execute_query("ALTER TABLE fluids ADD COLUMN top_up_percent REAL DEFAULT NULL;")
            logger.info("Added top_up_percent column to fluids table.")
    except Exception as e:
        logger.error(f"Error updating fluids table schema: {e}")

update_fluids_table_schema()

database.execute_query('''
    CREATE TABLE IF NOT EXISTS maintenance (
        maintenance_id INTEGER PRIMARY KEY AUTOINCREMENT,
        equipment_id INTEGER,
        maintenance_type TEXT,
        done_date DATE,
        due_date DATE,
        vintage_years REAL,
        meterage_kms REAL,
        FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
    );
''')

database.execute_query('''
    CREATE TABLE IF NOT EXISTS repairs (
        repair_id INTEGER PRIMARY KEY AUTOINCREMENT,
        equipment_id INTEGER,
        repair_type TEXT,
        description TEXT,
        repair_date DATE,
        FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
    );
''')

database.execute_query('''
    CREATE TABLE IF NOT EXISTS discard_criteria (
        discard_criteria_id INTEGER PRIMARY KEY AUTOINCREMENT,
        equipment_id INTEGER,
        component TEXT,
        criteria_years INTEGER,
        criteria_kms INTEGER,
        FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
    );
''')

database.execute_query('''
    CREATE TABLE IF NOT EXISTS tyre_maintenance (
        tyre_maintenance_id INTEGER PRIMARY KEY AUTOINCREMENT,
        equipment_id INTEGER,
        tyre_rotation_kms INTEGER,
        tyre_condition_kms INTEGER,
        tyre_condition_years INTEGER,
        last_rotation_date DATE,
        date_of_change DATE,
        FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
    );
''')

database.execute_query('''
    CREATE TABLE IF NOT EXISTS demand_forecast (
        demand_id INTEGER PRIMARY KEY AUTOINCREMENT,
        fluid_id INTEGER,
        fiscal_year INTEGER,
        total_requirement REAL,
        remarks TEXT,
        FOREIGN KEY (fluid_id) REFERENCES fluids (fluid_id)
    );
''')

database.execute_query('''
    CREATE TABLE IF NOT EXISTS overhauls (
        overhaul_id INTEGER PRIMARY KEY AUTOINCREMENT,
        equipment_id INTEGER,
        overhaul_type TEXT,
        overhaul_date DATE,
        meter_reading REAL,
        hours_reading REAL,
        notes TEXT,
        description TEXT,
        FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
    );
''')

database.execute_query('''
    CREATE TABLE IF NOT EXISTS overhaul_forecast (
        forecast_id INTEGER PRIMARY KEY AUTOINCREMENT,
        equipment_id INTEGER,
        fiscal_year INTEGER,
        overhaul_type TEXT,
        total_requirement REAL,
        remarks TEXT,
        FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
    );
''')

database.execute_query('''
    CREATE TABLE IF NOT EXISTS conditioning_forecast (
        forecast_id INTEGER PRIMARY KEY AUTOINCREMENT,
        equipment_id INTEGER,
        fiscal_year INTEGER,
        conditioning_type TEXT,
        total_requirement REAL,
        remarks TEXT,
        FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
    );
''')

database.execute_query('''
    CREATE TABLE IF NOT EXISTS battery (
        battery_id INTEGER PRIMARY KEY AUTOINCREMENT,
        equipment_id INTEGER,
        done_date DATE,
        custom_life_months INTEGER,
        FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
    );
''')

database.execute_query('''
    CREATE TABLE IF NOT EXISTS maintenance_archives (
        archive_id INTEGER PRIMARY KEY AUTOINCREMENT,
        archive_name TEXT NOT NULL,
        archive_type TEXT NOT NULL,
        maintenance_category TEXT NOT NULL,
        period_start DATE NOT NULL,
        period_end DATE NOT NULL,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by TEXT,
        record_count INTEGER DEFAULT 0,
        archive_data TEXT,
        notes TEXT
    );
''')

def update_overhauls_table_schema():
    """Update the overhauls table to add the 'description' column if it does not exist."""
    columns = database.execute_query("PRAGMA table_info(overhauls)", (), fetchall=True)
    if not any(col['name'] == 'description' for col in columns):
        database.execute_query("ALTER TABLE overhauls ADD COLUMN description TEXT", ())
        print("'description' column added to overhauls table")
    else:
        print("'description' column already exists in overhauls table")

# Update the overhauls table schema on module load
update_overhauls_table_schema()