"""
overhaul_service.py
Centralized service for all overhaul-related business logic in the ARMY Equipment Inventory project.
All overhaul status, due date, and data operations should go through this service to ensure consistency.
"""

import logging
from datetime import datetime, date, timedelta
import utils
from models import Overhaul, Equipment

logger = logging.getLogger("overhaul_service")


def get_overhaul_status(overhaul_type, due_date, done_date, date_of_commission=None, oh1_done_date=None, custom_intervals=None, meterage_km=None):
    """
    Centralized method to calculate overhaul status. All status calculations should use this.
    Returns status string (e.g., 'overdue', 'critical', 'warning', 'unknown').
    """
    try:
        return utils.calculate_overhaul_status(
            overhaul_type, due_date, done_date, date_of_commission, oh1_done_date, custom_intervals, meterage_km
        )
    except Exception as e:
        logger.error(f"Error calculating overhaul status: {e}")
        return "unknown"


def calculate_oh1_due_date(date_of_commission):
    """Calculate OH-I due date (15 years from commission)."""
    try:
        if isinstance(date_of_commission, str):
            commission_date = date.fromisoformat(date_of_commission.split(' ')[0])
        else:
            commission_date = date_of_commission

        # Use exact year calculation to handle leap years properly
        return commission_date.replace(year=commission_date.year + 15)
    except Exception as e:
        logger.error(f"Error calculating OH-I due date: {e}")
        return None


def calculate_oh2_due_date(oh1_done_date):
    """Calculate OH-II due date (10 years from OH-I completion)."""
    try:
        if isinstance(oh1_done_date, str):
            oh1_date = date.fromisoformat(oh1_done_date.split(' ')[0])
        else:
            oh1_date = oh1_done_date

        # Use exact year calculation to handle leap years properly
        return oh1_date.replace(year=oh1_date.year + 10)
    except Exception as e:
        logger.error(f"Error calculating OH-II due date: {e}")
        return None


def calculate_discard_date(oh2_done_date):
    """Calculate equipment discard date (10 years from OH-II completion)."""
    try:
        if isinstance(oh2_done_date, str):
            oh2_date = date.fromisoformat(oh2_done_date.split(' ')[0])
        else:
            oh2_date = oh2_done_date

        # Use exact year calculation to handle leap years properly
        return oh2_date.replace(year=oh2_date.year + 10)
    except Exception as e:
        logger.error(f"Error calculating discard date: {e}")
        return None


def auto_create_overhaul_records(equipment_id):
    """
    Automatically create overhaul records for equipment if they don't exist.
    This ensures every equipment has OH-I and OH-II tracking.
    """
    try:
        equipment = Equipment.get_by_id(equipment_id)
        if not equipment:
            logger.error(f"Equipment not found: {equipment_id}")
            return False

        existing_overhauls = Overhaul.get_by_equipment(equipment_id)
        oh1_exists = any(oh.get('overhaul_type') == 'OH-I' for oh in existing_overhauls)
        oh2_exists = any(oh.get('overhaul_type') == 'OH-II' for oh in existing_overhauls)

        commission_date = equipment.get('date_of_commission')
        if not commission_date:
            logger.warning(f"No commission date for equipment {equipment_id}")
            return False

        # Create OH-I record if missing
        if not oh1_exists:
            oh1_due = calculate_oh1_due_date(commission_date)
            if oh1_due:
                oh1_status = get_overhaul_status(
                    'OH-I', oh1_due, None,
                    date_of_commission=commission_date,
                    meterage_km=equipment.get('MeterageKMs') or equipment.get('meterage_kms')
                )

                oh1 = Overhaul(
                    equipment_id=equipment_id,
                    overhaul_type='OH-I',
                    due_date=oh1_due.isoformat(),
                    status=oh1_status,
                    description=f"Auto-generated OH-I record for {equipment.get('make_and_type', 'Unknown Equipment')}"
                )
                oh1.save()
                logger.info(f"Created OH-I record for equipment {equipment_id}")

        # Create OH-II record if missing (but only if OH-I exists or is completed)
        if not oh2_exists:
            oh1_record = next((oh for oh in existing_overhauls if oh.get('overhaul_type') == 'OH-I'), None)
            if not oh1_record:
                # Try to get the newly created OH-I record
                updated_overhauls = Overhaul.get_by_equipment(equipment_id)
                oh1_record = next((oh for oh in updated_overhauls if oh.get('overhaul_type') == 'OH-I'), None)

            if oh1_record:
                # If OH-I is completed, calculate OH-II due date from completion
                if oh1_record.get('done_date'):
                    oh2_due = calculate_oh2_due_date(oh1_record.get('done_date'))
                else:
                    # If OH-I not completed, estimate OH-II due date
                    oh1_due = oh1_record.get('due_date')
                    if oh1_due:
                        oh2_due = calculate_oh2_due_date(oh1_due)
                    else:
                        oh2_due = None

                if oh2_due:
                    oh2_status = get_overhaul_status(
                        'OH-II', oh2_due, None,
                        date_of_commission=commission_date,
                        oh1_done_date=oh1_record.get('done_date'),
                        meterage_km=equipment.get('MeterageKMs') or equipment.get('meterage_kms')
                    )

                    oh2 = Overhaul(
                        equipment_id=equipment_id,
                        overhaul_type='OH-II',
                        due_date=oh2_due.isoformat(),
                        status=oh2_status,
                        description=f"Auto-generated OH-II record for {equipment.get('make_and_type', 'Unknown Equipment')}"
                    )
                    oh2.save()
                    logger.info(f"Created OH-II record for equipment {equipment_id}")

        return True

    except Exception as e:
        logger.error(f"Error auto-creating overhaul records for equipment {equipment_id}: {e}")
        return False


def update_overhaul_statuses():
    """Update all overhaul statuses based on current dates and mileage."""
    try:
        all_overhauls = Overhaul.get_all()
        updated_count = 0

        for overhaul in all_overhauls:
            equipment_id = overhaul.get('equipment_id')
            equipment = Equipment.get_by_id(equipment_id)
            if not equipment:
                continue

            new_status = get_overhaul_status(
                overhaul.get('overhaul_type'),
                overhaul.get('due_date'),
                overhaul.get('done_date'),
                date_of_commission=equipment.get('date_of_commission'),
                oh1_done_date=overhaul.get('done_date') if overhaul.get('overhaul_type') == 'OH-I' else None,
                meterage_km=equipment.get('MeterageKMs') or equipment.get('meterage_kms')
            )

            if new_status != overhaul.get('status'):
                Overhaul.update(overhaul.get('overhaul_id'), status=new_status)
                updated_count += 1

        logger.info(f"Updated {updated_count} overhaul statuses")
        return updated_count

    except Exception as e:
        logger.error(f"Error updating overhaul statuses: {e}")
        return 0


def fetch_overhaul_by_equipment(equipment_id):
    """
    Fetch all overhauls for a given equipment ID.
    Returns a list of overhaul dicts.
    """
    try:
        return Overhaul.get_by_equipment(equipment_id)
    except Exception as e:
        logger.error(f"Error fetching overhauls for equipment {equipment_id}: {e}")
        return []


def save_overhaul(overhaul_data):
    """
    Save or update an overhaul record using the Overhaul model.
    Expects overhaul_data as a dict with all necessary fields.
    Returns overhaul_id on success, None on failure.
    """
    try:
        overhaul = Overhaul(**overhaul_data)
        return overhaul.save()
    except Exception as e:
        logger.error(f"Error saving overhaul: {e}")
        return None


def complete_overhaul(equipment_id, overhaul_type, completion_date, completed_by=None, completion_notes=None, meter_reading=None):
    """
    Complete an overhaul and trigger all dependent calculations.
    This is the main function for recording overhaul completions.
    """
    try:
        # Validate inputs
        if overhaul_type not in ['OH-I', 'OH-II']:
            raise ValueError(f"Invalid overhaul type: {overhaul_type}")

        # Get equipment
        equipment = Equipment.get_by_id(equipment_id)
        if not equipment:
            raise ValueError(f"Equipment not found: {equipment_id}")

        # Parse completion date
        if isinstance(completion_date, str):
            completion_date = date.fromisoformat(completion_date)

        # Validate completion date is not in future
        if completion_date > date.today():
            raise ValueError("Completion date cannot be in the future")

        # Get existing overhauls
        overhauls = Overhaul.get_by_equipment(equipment_id)
        oh1 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)
        oh2 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-II'), None)

        # Validation logic
        if overhaul_type == 'OH-I':
            # Validate OH-I completion
            oh1_due_date = calculate_oh1_due_date(equipment.get('date_of_commission'))
            if oh1_due_date and completion_date < oh1_due_date - timedelta(days=365):  # 1 year grace period
                raise ValueError(f"OH-I cannot be completed more than 1 year before due date ({oh1_due_date})")

            # Update OH-I record
            if oh1:
                Overhaul.update(oh1['overhaul_id'],
                              done_date=completion_date.isoformat(),
                              status='completed',
                              completed_by=completed_by,
                              completion_notes=completion_notes,
                              meter_reading=meter_reading or equipment.get('MeterageKMs') or equipment.get('meterage_kms'))
            else:
                # Create OH-I record if it doesn't exist
                oh1_due = calculate_oh1_due_date(equipment.get('date_of_commission'))
                oh1 = Overhaul(
                    equipment_id=equipment_id,
                    overhaul_type='OH-I',
                    done_date=completion_date.isoformat(),
                    due_date=oh1_due.isoformat() if oh1_due else None,
                    status='completed',
                    completed_by=completed_by,
                    completion_notes=completion_notes,
                    meter_reading=meter_reading or equipment.get('MeterageKMs') or equipment.get('meterage_kms')
                )
                oh1.save()

            # Calculate and create/update OH-II due date
            oh2_due_date = calculate_oh2_due_date(completion_date)
            if oh2:
                Overhaul.update(oh2['overhaul_id'], due_date=oh2_due_date.isoformat())
            else:
                oh2 = Overhaul(
                    equipment_id=equipment_id,
                    overhaul_type='OH-II',
                    due_date=oh2_due_date.isoformat(),
                    status='scheduled',
                    description=f"Auto-generated OH-II record after OH-I completion"
                )
                oh2.save()

            logger.info(f"OH-I completed for equipment {equipment_id}, OH-II due date set to {oh2_due_date}")

        elif overhaul_type == 'OH-II':
            # Validate OH-I is completed first
            if not oh1 or not oh1.get('done_date'):
                raise ValueError("OH-I must be completed before OH-II can be completed")

            # Validate OH-II completion date is after OH-I completion
            oh1_done_date = date.fromisoformat(oh1.get('done_date'))
            if completion_date < oh1_done_date:
                raise ValueError("OH-II completion date cannot be before OH-I completion date")

            # Update OH-II record
            if oh2:
                Overhaul.update(oh2['overhaul_id'],
                              done_date=completion_date.isoformat(),
                              status='completed',
                              completed_by=completed_by,
                              completion_notes=completion_notes,
                              meter_reading=meter_reading or equipment.get('MeterageKMs') or equipment.get('meterage_kms'))
            else:
                # Create OH-II record if it doesn't exist
                oh2_due = calculate_oh2_due_date(oh1_done_date)
                oh2 = Overhaul(
                    equipment_id=equipment_id,
                    overhaul_type='OH-II',
                    done_date=completion_date.isoformat(),
                    due_date=oh2_due.isoformat() if oh2_due else None,
                    status='completed',
                    completed_by=completed_by,
                    completion_notes=completion_notes,
                    meter_reading=meter_reading or equipment.get('MeterageKMs') or equipment.get('meterage_kms')
                )
                oh2.save()

            # Calculate discard date and move to discard criteria
            discard_date = calculate_discard_date(completion_date)
            move_to_discard_criteria(equipment_id, discard_date)

            logger.info(f"OH-II completed for equipment {equipment_id}, moved to discard criteria with discard date {discard_date}")

        # Update all overhaul statuses for this equipment
        update_equipment_overhaul_statuses(equipment_id)

        return True

    except Exception as e:
        logger.error(f"Error completing {overhaul_type} for equipment {equipment_id}: {e}")
        raise e


def move_to_discard_criteria(equipment_id, discard_date):
    """Move equipment to discard criteria tab after OH-II completion."""
    try:
        from models import DiscardCriteria

        # Check if equipment already has discard criteria
        existing_criteria = DiscardCriteria.get_by_equipment(equipment_id)

        if not existing_criteria:
            # Calculate criteria based on discard date
            equipment = Equipment.get_by_id(equipment_id)
            if equipment:
                # Use date_of_commission as the primary field, fallback to date_of_induction for compatibility
                commission_date = equipment.date_of_commission or getattr(equipment, 'date_of_induction', None)
                if commission_date:
                    if isinstance(commission_date, str):
                        commission_date = date.fromisoformat(commission_date.split(' ')[0])

                    # Calculate years from commission to discard
                    years_to_discard = (discard_date - commission_date).days / 365.25

                    # Set criteria slightly below current values to trigger discard status
                    current_vintage = equipment.vintage_years or 0
                    current_meterage = equipment.meterage_kms or 0

                    criteria = DiscardCriteria(
                        equipment_id=equipment_id,
                        criteria_years=max(years_to_discard - 1, current_vintage - 1),  # Ensure it triggers
                        criteria_kms=max(current_meterage - 1000, 0)  # Ensure it triggers
                    )
                    criteria.save()
                    logger.info(f"Created discard criteria for equipment {equipment_id}")

    except Exception as e:
        logger.error(f"Error moving equipment {equipment_id} to discard criteria: {e}")


def check_and_create_discard_criteria():
    """Check all equipment and create discard criteria for those that completed OH-II + 10 years."""
    try:
        from models import Equipment, Overhaul, DiscardCriteria
        from datetime import date, timedelta

        logger.info("Checking equipment for discard criteria creation...")

        # Get all equipment
        equipment_list = Equipment.get_all()
        created_count = 0

        for equipment in equipment_list:
            equipment_id = equipment['equipment_id']

            # Check if equipment already has discard criteria
            existing_criteria = DiscardCriteria.get_by_equipment(equipment_id)
            if existing_criteria:
                continue  # Skip if already has criteria

            # Get OH-II overhaul for this equipment
            overhauls = Overhaul.get_by_equipment(equipment_id)
            oh2_overhaul = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-II'), None)

            if oh2_overhaul and oh2_overhaul.get('done_date'):
                try:
                    # Parse OH-II completion date
                    oh2_done_str = oh2_overhaul.get('done_date')
                    if isinstance(oh2_done_str, str):
                        oh2_done_date = date.fromisoformat(oh2_done_str.split(' ')[0])
                    else:
                        oh2_done_date = oh2_done_str

                    # Calculate discard date (OH-II + 10 years)
                    discard_date = oh2_done_date + timedelta(days=365*10)

                    # Check if equipment is due for discard (current date >= discard date)
                    if date.today() >= discard_date:
                        # Create discard criteria
                        # Use date_of_commission as primary field, fallback to date_of_induction for compatibility
                        commission_date = equipment.get('date_of_commission') or equipment.get('date_of_induction')
                        if commission_date:
                            if isinstance(commission_date, str):
                                commission_date = date.fromisoformat(commission_date.split(' ')[0])

                            # Calculate total years from commission to now
                            total_years = (date.today() - commission_date).days / 365.25

                            # Set criteria to current equipment values to trigger discard status
                            current_vintage = equipment.get('vintage_years') or total_years
                            current_meterage = equipment.get('meterage_kms') or equipment.get('MeterageKMs') or 0

                            criteria = DiscardCriteria(
                                equipment_id=equipment_id,
                                criteria_years=int(total_years),  # Use actual years since commission
                                criteria_kms=int(current_meterage) if current_meterage > 0 else 0
                            )
                            criteria.save()
                            created_count += 1
                            logger.info(f"Created discard criteria for equipment {equipment_id} (OH-II completed {oh2_done_str}, discard due {discard_date})")

                except Exception as e:
                    logger.error(f"Error processing equipment {equipment_id} for discard criteria: {e}")

        logger.info(f"Discard criteria check complete. Created {created_count} new criteria records.")
        return created_count

    except Exception as e:
        logger.error(f"Error in check_and_create_discard_criteria: {e}")
        return 0


def update_equipment_overhaul_statuses(equipment_id):
    """Update all overhaul statuses for a specific equipment."""
    try:
        equipment = Equipment.get_by_id(equipment_id)
        if not equipment:
            return

        overhauls = Overhaul.get_by_equipment(equipment_id)

        for overhaul in overhauls:
            # Get OH-I done date for OH-II calculations
            oh1_done_date = None
            if overhaul.get('overhaul_type') == 'OH-II':
                oh1 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)
                oh1_done_date = oh1.get('done_date') if oh1 else None

            new_status = get_overhaul_status(
                overhaul.get('overhaul_type'),
                overhaul.get('due_date'),
                overhaul.get('done_date'),
                date_of_commission=equipment.get('date_of_commission'),
                oh1_done_date=oh1_done_date,
                meterage_km=equipment.get('MeterageKMs') or equipment.get('meterage_kms')
            )

            if new_status != overhaul.get('status'):
                Overhaul.update(overhaul.get('overhaul_id'), status=new_status)

        logger.info(f"Updated overhaul statuses for equipment {equipment_id}")

    except Exception as e:
        logger.error(f"Error updating overhaul statuses for equipment {equipment_id}: {e}")


def get_overhaul_summary(equipment_id):
    """Get a comprehensive overhaul summary for equipment."""
    try:
        equipment = Equipment.get_by_id(equipment_id)
        if not equipment:
            return None

        overhauls = Overhaul.get_by_equipment(equipment_id)
        oh1 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)
        oh2 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-II'), None)

        summary = {
            'equipment_id': equipment_id,
            'equipment_name': utils.format_equipment_display(equipment),
            'commission_date': equipment.get('date_of_commission'),
            'current_meterage': equipment.get('MeterageKMs') or equipment.get('meterage_kms'),
            'oh1': {
                'due_date': oh1.get('due_date') if oh1 else calculate_oh1_due_date(equipment.get('date_of_commission')),
                'done_date': oh1.get('done_date') if oh1 else None,
                'status': oh1.get('status') if oh1 else 'scheduled',
                'overdue_by_km': False,
                'overdue_by_date': False,
                'can_complete': True
            },
            'oh2': {
                'due_date': oh2.get('due_date') if oh2 else None,
                'done_date': oh2.get('done_date') if oh2 else None,
                'status': oh2.get('status') if oh2 else 'scheduled',
                'can_complete': False
            },
            'discard_date': None,
            'recommendation': 'normal'
        }

        # Calculate additional details
        current_km = equipment.get('MeterageKMs') or equipment.get('meterage_kms') or 0
        if isinstance(current_km, str):
            try:
                current_km = float(current_km)
            except:
                current_km = 0

        # OH-I analysis
        if current_km >= 60000:
            summary['oh1']['overdue_by_km'] = True
            summary['recommendation'] = 'urgent_oh1'
        elif current_km >= 55000:
            summary['recommendation'] = 'plan_oh1'

        # OH-II completion eligibility
        if oh1 and oh1.get('done_date') and oh1.get('done_date') not in [None, '', 'None']:
            summary['oh2']['can_complete'] = True

        # OH-II analysis
        if oh2 and oh2.get('done_date'):
            discard_date = calculate_discard_date(oh2.get('done_date'))
            summary['discard_date'] = discard_date.isoformat() if discard_date else None
            if discard_date and date.today() >= discard_date:
                summary['recommendation'] = 'discard'

        return summary

    except Exception as e:
        logger.error(f"Error getting overhaul summary for equipment {equipment_id}: {e}")
        return None


def validate_overhaul_completion(equipment_id, overhaul_type, completion_date):
    """
    Comprehensive validation for overhaul completion.
    Returns (is_valid, error_message)
    """
    try:
        # Get equipment
        equipment = Equipment.get_by_id(equipment_id)
        if not equipment:
            return False, f"Equipment not found: {equipment_id}"

        # Parse completion date
        if isinstance(completion_date, str):
            completion_date = date.fromisoformat(completion_date)

        # Basic validations
        if completion_date > date.today():
            return False, "Completion date cannot be in the future"

        # Get commission date
        commission_date = equipment.get('date_of_commission')
        if not commission_date:
            return False, "Equipment must have a commission date for overhaul tracking"

        if isinstance(commission_date, str):
            commission_date = date.fromisoformat(commission_date.split(' ')[0])

        if completion_date < commission_date:
            return False, "Completion date cannot be before equipment commission date"

        # Get existing overhauls
        overhauls = Overhaul.get_by_equipment(equipment_id)
        oh1 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)
        oh2 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-II'), None)

        if overhaul_type == 'OH-I':
            # OH-I specific validations
            oh1_due_date = calculate_oh1_due_date(commission_date)
            if oh1_due_date:
                # Allow completion up to 1 year before due date
                earliest_completion = oh1_due_date - timedelta(days=365)
                if completion_date < earliest_completion:
                    return False, f"OH-I cannot be completed before {earliest_completion.strftime('%Y-%m-%d')} (1 year before due date)"

            # Check if already completed
            if oh1 and oh1.get('done_date') and oh1.get('done_date') not in [None, '', 'None']:
                return False, "OH-I is already completed"

        elif overhaul_type == 'OH-II':
            # OH-II specific validations
            if not oh1:
                return False, "OH-I record must exist before OH-II can be completed"

            oh1_done_date = oh1.get('done_date')
            if not oh1_done_date or oh1_done_date in [None, '', 'None']:
                return False, "OH-I must be completed before OH-II can be completed"

            # Parse OH-I completion date
            if isinstance(oh1_done_date, str):
                oh1_done_date = date.fromisoformat(oh1_done_date.split(' ')[0])

            if completion_date < oh1_done_date:
                return False, "OH-II completion date cannot be before OH-I completion date"

            # Check if already completed
            if oh2 and oh2.get('done_date') and oh2.get('done_date') not in [None, '', 'None']:
                return False, "OH-II is already completed"

            # Check minimum time between overhauls (e.g., at least 1 year)
            min_gap = oh1_done_date + timedelta(days=365)
            if completion_date < min_gap:
                return False, f"OH-II cannot be completed before {min_gap.strftime('%Y-%m-%d')} (minimum 1 year after OH-I)"

        else:
            return False, f"Invalid overhaul type: {overhaul_type}"

        return True, "Validation passed"

    except Exception as e:
        logger.error(f"Error validating overhaul completion: {e}")
        return False, f"Validation error: {str(e)}"


def get_overhaul_lifecycle_status(equipment_id):
    """
    Get complete lifecycle status for equipment overhauls.
    Returns detailed status information for planning and tracking.
    """
    try:
        equipment = Equipment.get_by_id(equipment_id)
        if not equipment:
            return None

        overhauls = Overhaul.get_by_equipment(equipment_id)
        oh1 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)
        oh2 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-II'), None)

        commission_date = equipment.get('date_of_commission')
        current_meterage = equipment.get('MeterageKMs') or equipment.get('meterage_kms') or 0

        # Parse commission date
        if isinstance(commission_date, str):
            commission_date = date.fromisoformat(commission_date.split(' ')[0])

        # Calculate key dates
        oh1_due_date = calculate_oh1_due_date(commission_date) if commission_date else None
        oh2_due_date = None
        discard_date = None

        # OH-I status
        oh1_status = {
            'due_date': oh1_due_date.isoformat() if oh1_due_date else None,
            'done_date': oh1.get('done_date') if oh1 else None,
            'status': oh1.get('status') if oh1 else 'scheduled',
            'is_completed': False,
            'is_overdue': False,
            'days_until_due': None,
            'km_until_due': None
        }

        if oh1 and oh1.get('done_date') and oh1.get('done_date') not in [None, '', 'None']:
            oh1_status['is_completed'] = True
            oh1_status['status'] = 'completed'

            # Calculate OH-II due date
            oh1_done_date = date.fromisoformat(oh1.get('done_date').split(' ')[0])
            oh2_due_date = calculate_oh2_due_date(oh1_done_date)

        # Calculate OH-I urgency
        if oh1_due_date and not oh1_status['is_completed']:
            days_until = (oh1_due_date - date.today()).days
            oh1_status['days_until_due'] = days_until
            oh1_status['is_overdue'] = days_until < 0

            # KM-based urgency
            try:
                current_km = float(current_meterage)
                oh1_status['km_until_due'] = max(0, 60000 - current_km)
                if current_km >= 60000:
                    oh1_status['is_overdue'] = True
            except:
                pass

        # OH-II status
        oh2_status = {
            'due_date': oh2_due_date.isoformat() if oh2_due_date else None,
            'done_date': oh2.get('done_date') if oh2 else None,
            'status': oh2.get('status') if oh2 else 'scheduled',
            'is_completed': False,
            'is_overdue': False,
            'days_until_due': None,
            'can_start': oh1_status['is_completed']
        }

        if oh2 and oh2.get('done_date') and oh2.get('done_date') not in [None, '', 'None']:
            oh2_status['is_completed'] = True
            oh2_status['status'] = 'completed'

            # Calculate discard date
            oh2_done_date = date.fromisoformat(oh2.get('done_date').split(' ')[0])
            discard_date = calculate_discard_date(oh2_done_date)

        # Calculate OH-II urgency
        if oh2_due_date and not oh2_status['is_completed']:
            days_until = (oh2_due_date - date.today()).days
            oh2_status['days_until_due'] = days_until
            oh2_status['is_overdue'] = days_until < 0

        # Overall lifecycle status
        lifecycle_phase = 'operational'
        if oh2_status['is_completed']:
            if discard_date and date.today() >= discard_date:
                lifecycle_phase = 'discard'
            else:
                lifecycle_phase = 'post_oh2'
        elif oh1_status['is_completed']:
            lifecycle_phase = 'post_oh1'
        elif oh1_status['is_overdue']:
            lifecycle_phase = 'oh1_overdue'

        return {
            'equipment_id': equipment_id,
            'equipment_name': utils.format_equipment_display(equipment),
            'commission_date': commission_date.isoformat() if commission_date else None,
            'current_meterage': current_meterage,
            'lifecycle_phase': lifecycle_phase,
            'oh1': oh1_status,
            'oh2': oh2_status,
            'discard_date': discard_date.isoformat() if discard_date else None,
            'next_action': get_next_recommended_action(oh1_status, oh2_status, lifecycle_phase)
        }

    except Exception as e:
        logger.error(f"Error getting overhaul lifecycle status for equipment {equipment_id}: {e}")
        return None


def get_next_recommended_action(oh1_status, oh2_status, lifecycle_phase):
    """Determine the next recommended action for equipment."""
    if lifecycle_phase == 'discard':
        return 'Equipment should be discarded'
    elif lifecycle_phase == 'oh1_overdue':
        return 'OH-I is overdue - schedule immediately'
    elif lifecycle_phase == 'post_oh2':
        return 'Monitor for discard criteria'
    elif lifecycle_phase == 'post_oh1':
        if oh2_status['is_overdue']:
            return 'OH-II is overdue - schedule immediately'
        elif oh2_status['days_until_due'] and oh2_status['days_until_due'] < 365:
            return 'Plan OH-II within next year'
        else:
            return 'Continue normal operations'
    else:
        if oh1_status['is_overdue']:
            return 'OH-I is overdue - schedule immediately'
        elif oh1_status['days_until_due'] and oh1_status['days_until_due'] < 365:
            return 'Plan OH-I within next year'
        else:
            return 'Continue normal operations'


def auto_populate_discard_criteria_from_overhaul_status():
    """
    Automatically populate discard criteria tab when equipment shows 'discard' status in overhaul tab.
    This function monitors all equipment and creates discard criteria entries for equipment
    that have transitioned to 'discard' status based on OH-II completion + 10 years.
    """
    try:
        from models import Equipment, Overhaul, DiscardCriteria
        from datetime import date, timedelta

        logger.info("Auto-populating discard criteria from overhaul status...")

        # Get all active equipment (not already discarded)
        equipment_list = Equipment.get_active()
        created_count = 0
        updated_count = 0

        for equipment in equipment_list:
            equipment_id = equipment['equipment_id']

            # Get overhauls for this equipment
            overhauls = Overhaul.get_by_equipment(equipment_id)
            oh2_overhaul = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-II'), None)

            # Check if OH-II is completed
            if oh2_overhaul and oh2_overhaul.get('done_date'):
                try:
                    # Parse OH-II completion date
                    oh2_done_str = oh2_overhaul.get('done_date')
                    if isinstance(oh2_done_str, str):
                        oh2_done_date = date.fromisoformat(oh2_done_str.split(' ')[0])
                    else:
                        oh2_done_date = oh2_done_str

                    # Calculate overhaul status to check if it's "discard"
                    oh1_overhaul = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)
                    oh1_done_date = oh1_overhaul.get('done_date') if oh1_overhaul else None

                    current_status = get_overhaul_status(
                        'OH-II',
                        oh2_overhaul.get('due_date'),
                        oh2_done_str,
                        date_of_commission=equipment.get('date_of_commission'),
                        oh1_done_date=oh1_done_date,
                        meterage_km=equipment.get('meterage_kms') or equipment.get('MeterageKMs')
                    )

                    # If status is "discard", create/update discard criteria
                    if current_status == "discard":
                        # Check if discard criteria already exists
                        existing_criteria = DiscardCriteria.get_by_equipment(equipment_id)

                        # Calculate criteria values according to requirements
                        commission_date = equipment.get('date_of_commission') or equipment.get('date_of_induction')
                        if commission_date:
                            if isinstance(commission_date, str):
                                commission_date = date.fromisoformat(commission_date.split(' ')[0])

                            # Calculate total years from commission to OH-II completion + 10 years
                            discard_date = oh2_done_date + timedelta(days=365*10)
                            total_days = (discard_date - commission_date).days
                            total_years = total_days / 365.25

                            # Get current meterage
                            current_meterage = equipment.get('meterage_kms') or equipment.get('MeterageKMs') or 0
                            if isinstance(current_meterage, str):
                                try:
                                    current_meterage = float(current_meterage)
                                except:
                                    current_meterage = 0

                            if existing_criteria:
                                # Update existing criteria with calculated values
                                criteria = DiscardCriteria(
                                    discard_criteria_id=existing_criteria[0]['discard_criteria_id'],
                                    equipment_id=equipment_id,
                                    criteria_years=int(total_years),
                                    criteria_kms=int(current_meterage)
                                )
                                criteria.save()
                                updated_count += 1
                                logger.info(f"Updated discard criteria for equipment {equipment_id} (status: discard)")
                            else:
                                # Create new discard criteria
                                criteria = DiscardCriteria(
                                    equipment_id=equipment_id,
                                    criteria_years=int(total_years),
                                    criteria_kms=int(current_meterage)
                                )
                                criteria.save()
                                created_count += 1
                                logger.info(f"Created discard criteria for equipment {equipment_id} (status: discard)")

                except Exception as e:
                    logger.error(f"Error processing equipment {equipment_id} for auto discard criteria: {e}")

        logger.info(f"Auto discard criteria population complete. Created: {created_count}, Updated: {updated_count}")
        return created_count + updated_count

    except Exception as e:
        logger.error(f"Error in auto_populate_discard_criteria_from_overhaul_status: {e}")
        return 0


def log_overhaul_operation(message, level="info"):
    if level == "error":
        logger.error(message)
    else:
        logger.info(message)
