"""Discard criteria management widget for the equipment inventory application."""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QPushButton, QTableWidget, QTableWidgetItem,
                           QHeaderView, QAbstractItemView, QMessageBox,
                           QComboBox, QLineEdit, QFormLayout, QGroupBox,
                           QSplitter, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor

import models
from ui.custom_widgets import ReadOnlyTableWidget, StatusLabel
from ui.dialogs import DiscardCriteriaDialog
import utils
from datetime import datetime
from ui.common_styles import *

class DiscardCriteriaWidget(QWidget):
    """Widget for managing equipment discard criteria."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """Set up the discard criteria widget UI."""
        # Main layout
        main_layout = QVBoxLayout(self)
        apply_standard_layout(main_layout)
        
        # Title
        title_label = QLabel("Discard Criteria Management")
        title_label.setObjectName("titleLabel")
        title_label.setStyleSheet("""
            QLabel#titleLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # Create a splitter for the main content
        content_splitter = QSplitter(Qt.Horizontal)
        
        # Left side - Discard criteria list
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # Search and filter controls
        filter_layout = QHBoxLayout()
        
        # Search box
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search criteria...")
        self.search_edit.textChanged.connect(self.filter_criteria)
        filter_layout.addWidget(QLabel("Search:"))
        filter_layout.addWidget(self.search_edit)
        
        # Equipment filter
        self.equipment_filter = QComboBox()
        self.equipment_filter.addItem("All Equipment", None)
        self.equipment_filter.currentIndexChanged.connect(self.filter_criteria)
        filter_layout.addWidget(QLabel("Equipment:"))
        filter_layout.addWidget(self.equipment_filter)
        
        # Status filter
        self.status_filter = QComboBox()
        self.status_filter.addItem("All Status", None)
        self.status_filter.addItem("Normal", "normal")
        self.status_filter.addItem("Meeting Discard Criteria", "discard")
        self.status_filter.currentIndexChanged.connect(self.filter_criteria)
        filter_layout.addWidget(QLabel("Status:"))
        filter_layout.addWidget(self.status_filter)
        
        filter_layout.addStretch()
        left_layout.addLayout(filter_layout)
        
        # Discard criteria table
        self.criteria_table = ReadOnlyTableWidget()
        self.criteria_table.setColumnCount(6)
        self.criteria_table.setHorizontalHeaderLabels([
            "BA Number", "Make and Type", "Date of Commission", "Status", "Years", "KMs"
        ])
        self.criteria_table.verticalHeader().setVisible(False)
        self.criteria_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.criteria_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.criteria_table.setSortingEnabled(True)
        self.criteria_table.row_clicked.connect(self.criteria_selected)
        
        # Set column widths
        header = self.criteria_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.resizeSection(0, 120)  # BA Number
        header.resizeSection(1, 200)  # Make and Type
        header.resizeSection(2, 120)  # Date of Commission
        header.resizeSection(3, 180)  # Status
        header.resizeSection(4, 80)   # Years
        header.resizeSection(5, 80)   # KMs
        
        left_layout.addWidget(self.criteria_table)
        
        # Buttons
        button_layout = QHBoxLayout()
        apply_button_layout(button_layout)

        self.add_button = QPushButton("Add Criteria")
        self.add_button.clicked.connect(self.add_criteria)
        apply_button_style(self.add_button, "primary")
        button_layout.addWidget(self.add_button)

        self.edit_button = QPushButton("Edit Criteria")
        self.edit_button.clicked.connect(self.edit_criteria)
        self.edit_button.setEnabled(False)
        apply_button_style(self.edit_button, "default")
        button_layout.addWidget(self.edit_button)

        self.delete_button = QPushButton("Delete Criteria")
        self.delete_button.clicked.connect(self.delete_criteria)
        self.delete_button.setEnabled(False)
        apply_button_style(self.delete_button, "danger")
        button_layout.addWidget(self.delete_button)

        button_layout.addStretch()

        self.create_demand_button = QPushButton("Create Demand")
        self.create_demand_button.clicked.connect(self.create_demand)
        self.create_demand_button.setEnabled(False)
        apply_button_style(self.create_demand_button, "danger")
        button_layout.addWidget(self.create_demand_button)
        
        left_layout.addLayout(button_layout)
        
        # Right side - Criteria details and editing
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # Equipment details group
        details_group = QGroupBox("Equipment Details")
        details_layout = QFormLayout(details_group)
        
        self.detail_ba_number = QLabel("--")
        details_layout.addRow("BA Number:", self.detail_ba_number)
        
        self.detail_make_type = QLabel("--")
        details_layout.addRow("Make & Type:", self.detail_make_type)
        
        self.detail_date_commission = QLabel("--")
        details_layout.addRow("Date of Commission:", self.detail_date_commission)
        
        self.detail_vintage = QLabel("--")
        details_layout.addRow("Current Vintage:", self.detail_vintage)
        
        self.detail_meterage = QLabel("--")
        details_layout.addRow("Current Meterage:", self.detail_meterage)
        
        self.detail_status = StatusLabel("--")
        details_layout.addRow("Status:", self.detail_status)
        
        right_layout.addWidget(details_group)
        
        # Criteria editing group
        criteria_group = QGroupBox("Discard Criteria")
        criteria_layout = QFormLayout(criteria_group)
        
        self.criteria_years_label = QLabel("--")
        criteria_layout.addRow("Years Criteria:", self.criteria_years_label)
        
        self.criteria_kms_label = QLabel("--")
        criteria_layout.addRow("KMs Criteria:", self.criteria_kms_label)
        
        # Quick edit section
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        criteria_layout.addRow(separator)
        
        quick_edit_label = QLabel("Quick Edit:")
        quick_edit_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        criteria_layout.addRow(quick_edit_label)
        
        self.edit_years_input = QLineEdit()
        self.edit_years_input.setPlaceholderText("Enter years...")
        self.edit_years_input.setEnabled(False)
        criteria_layout.addRow("Years:", self.edit_years_input)
        
        self.edit_kms_input = QLineEdit()
        self.edit_kms_input.setPlaceholderText("Enter KMs...")
        self.edit_kms_input.setEnabled(False)
        criteria_layout.addRow("KMs:", self.edit_kms_input)
        
        self.save_criteria_button = QPushButton("Save Changes")
        self.save_criteria_button.clicked.connect(self.save_criteria_changes)
        self.save_criteria_button.setEnabled(False)
        criteria_layout.addRow(self.save_criteria_button)

        # Add discard button
        self.discard_button = QPushButton("Mark as Discarded")
        self.discard_button.clicked.connect(self.mark_equipment_discarded)
        self.discard_button.setEnabled(False)
        self.discard_button.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #b71c1c;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        criteria_layout.addRow(self.discard_button)
        
        right_layout.addWidget(criteria_group)
        
        # Status summary
        summary_group = QGroupBox("Summary")
        summary_layout = QVBoxLayout(summary_group)
        
        self.summary_label = QLabel("Total Equipment: 0\nNormal: 0\nMeeting Discard Criteria: 0")
        self.summary_label.setStyleSheet("font-family: monospace; padding: 10px;")
        summary_layout.addWidget(self.summary_label)
        
        right_layout.addWidget(summary_group)
        
        right_layout.addStretch()
        
        # Add widgets to splitter
        content_splitter.addWidget(left_widget)
        content_splitter.addWidget(right_widget)
        content_splitter.setSizes([int(self.width() * 0.7), int(self.width() * 0.3)])
        
        main_layout.addWidget(content_splitter)

        # Apply standardized stylesheet
        self.setStyleSheet(get_complete_stylesheet())
    
    def load_data(self):
        """Load discard criteria data into the table."""
        # Check and create discard criteria for equipment that completed OH-II + 10 years
        try:
            import overhaul_service
            overhaul_service.check_and_create_discard_criteria()
        except Exception as e:
            print(f"Error checking discard criteria: {e}")

        # Get all discard criteria with equipment details
        criteria_list = models.DiscardCriteria.get_all()

        # Load equipment filter
        self.load_equipment_filter()

        # Clear table
        self.criteria_table.setRowCount(0)
        
        normal_count = 0
        discard_count = 0
        
        # Add data to table
        for row, criteria in enumerate(criteria_list):
            self.criteria_table.insertRow(row)
            
            # BA Number
            ba_number = criteria.get('ba_number') or '--'
            ba_item = QTableWidgetItem(str(ba_number))
            ba_item.setData(Qt.UserRole, criteria.get('discard_criteria_id'))
            ba_item.setData(Qt.UserRole + 1, criteria.get('equipment_id'))
            self.criteria_table.setItem(row, 0, ba_item)
            
            # Make and Type
            make_type = criteria.get('make_and_type') or '--'
            self.criteria_table.setItem(row, 1, QTableWidgetItem(str(make_type)))
            
            # Date of Commission
            date_commission = criteria.get('date_of_commission') or criteria.get('date_of_induction')
            if date_commission:
                try:
                    if isinstance(date_commission, str):
                        date_obj = datetime.strptime(date_commission, '%Y-%m-%d')
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    else:
                        formatted_date = str(date_commission)
                except:
                    formatted_date = str(date_commission)
            else:
                formatted_date = '--'
            self.criteria_table.setItem(row, 2, QTableWidgetItem(formatted_date))
            
            # Status calculation
            current_vintage = float(criteria.get('vintage_years') or 0)
            current_meterage = float(criteria.get('meterage_kms') or 0)
            criteria_years = float(criteria.get('criteria_years') or 0)
            criteria_kms = float(criteria.get('criteria_kms') or 0)
            
            # Both criteria must be met for discard status
            meets_discard = False
            if criteria_years > 0 and criteria_kms > 0:
                meets_discard = (current_vintage >= criteria_years) and (current_meterage >= criteria_kms)
            elif criteria_years > 0:
                meets_discard = current_vintage >= criteria_years
            elif criteria_kms > 0:
                meets_discard = current_meterage >= criteria_kms
            
            if meets_discard:
                status_text = "Meeting Discard Criteria"
                status_color = QColor("#e74c3c")  # Red
                discard_count += 1
            else:
                status_text = "Normal"
                status_color = QColor("#27ae60")  # Green
                normal_count += 1
            
            status_item = QTableWidgetItem(status_text)
            status_item.setForeground(status_color)
            status_item.setData(Qt.UserRole, "discard" if meets_discard else "normal")
            self.criteria_table.setItem(row, 3, status_item)
            
            # Years
            years_text = str(int(criteria_years)) if criteria_years > 0 else '--'
            self.criteria_table.setItem(row, 4, QTableWidgetItem(years_text))
            
            # KMs
            kms_text = str(int(criteria_kms)) if criteria_kms > 0 else '--'
            self.criteria_table.setItem(row, 5, QTableWidgetItem(kms_text))
        
        # Update summary
        total_count = len(criteria_list)
        self.summary_label.setText(
            f"Total Equipment: {total_count}\n"
            f"Normal: {normal_count}\n"
            f"Meeting Discard Criteria: {discard_count}"
        )
        
        # Resize columns to content
        self.criteria_table.resizeColumnsToContents()
        
        # Clear details
        self.clear_criteria_details()
    
    def load_equipment_filter(self):
        """Load equipment filter dropdown."""
        self.equipment_filter.clear()
        self.equipment_filter.addItem("All Equipment", None)
        
        equipment_list = models.Equipment.get_active()
        
        for equipment in equipment_list:
            ba_number = equipment.get('ba_number') or ''
            make_type = equipment.get('make_and_type') or ''
            display_text = f"{ba_number} - {make_type}" if ba_number else make_type
            equipment_id = equipment.get('equipment_id')
            self.equipment_filter.addItem(display_text, equipment_id)
    
    def filter_criteria(self):
        """Filter criteria list based on search text and filters."""
        search_text = self.search_edit.text().lower()
        equipment_id = self.equipment_filter.currentData()
        status_filter = self.status_filter.currentData()
        
        for row in range(self.criteria_table.rowCount()):
            show_row = True
            
            if search_text:
                row_text = ""
                for col in range(self.criteria_table.columnCount()):
                    item = self.criteria_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                
                if search_text not in row_text:
                    show_row = False
            
            if equipment_id is not None:
                item = self.criteria_table.item(row, 0)
                item_equipment_id = item.data(Qt.UserRole + 1)
                if item_equipment_id != equipment_id:
                    show_row = False
            
            if status_filter is not None:
                status_item = self.criteria_table.item(row, 3)
                item_status = status_item.data(Qt.UserRole)
                if item_status != status_filter:
                    show_row = False
            
            self.criteria_table.setRowHidden(row, not show_row)
    
    def criteria_selected(self, row):
        """Handle criteria selection."""
        self.edit_button.setEnabled(True)
        self.delete_button.setEnabled(True)
        self.save_criteria_button.setEnabled(True)
        self.edit_years_input.setEnabled(True)
        self.edit_kms_input.setEnabled(True)
        
        criteria_id = self.criteria_table.item(row, 0).data(Qt.UserRole)
        equipment_id = self.criteria_table.item(row, 0).data(Qt.UserRole + 1)
        
        status_item = self.criteria_table.item(row, 3)
        meets_discard = status_item.data(Qt.UserRole) == "discard"
        self.create_demand_button.setEnabled(meets_discard)
        self.discard_button.setEnabled(meets_discard)  # Enable discard button for equipment meeting criteria
        
        criteria = models.DiscardCriteria.get_by_id(criteria_id)
        if not criteria:
            return
        
        self.detail_ba_number.setText(str(criteria.get('ba_number') or '--'))
        self.detail_make_type.setText(str(criteria.get('make_and_type') or '--'))
        
        date_commission = criteria.get('date_of_commission') or criteria.get('date_of_induction')
        if date_commission:
            try:
                if isinstance(date_commission, str):
                    date_obj = datetime.strptime(date_commission, '%Y-%m-%d')
                    formatted_date = date_obj.strftime('%d/%m/%Y')
                else:
                    formatted_date = str(date_commission)
            except:
                formatted_date = str(date_commission)
        else:
            formatted_date = '--'
        self.detail_date_commission.setText(formatted_date)
        
        current_vintage = float(criteria.get('vintage_years') or 0)
        self.detail_vintage.setText(f"{utils.format_decimal(current_vintage, 2)} years")
        
        current_meterage = float(criteria.get('meterage_kms') or 0)
        self.detail_meterage.setText(f"{utils.format_decimal(current_meterage, 2)} KMs")
        
        criteria_years = float(criteria.get('criteria_years') or 0)
        criteria_kms = float(criteria.get('criteria_kms') or 0)
        
        meets_discard = False
        if criteria_years > 0 and criteria_kms > 0:
            meets_discard = (current_vintage >= criteria_years) and (current_meterage >= criteria_kms)
        elif criteria_years > 0:
            meets_discard = current_vintage >= criteria_years
        elif criteria_kms > 0:
            meets_discard = current_meterage >= criteria_kms
        
        if meets_discard:
            status_text = "Meeting Discard Criteria"
            status = "critical"
        else:
            status_text = "Normal"
            status = "normal"
        
        self.detail_status.setText(status_text)
        self.detail_status.setStatus(status)
        
        self.criteria_years_label.setText(str(int(criteria_years)) if criteria_years > 0 else '--')
        self.criteria_kms_label.setText(str(int(criteria_kms)) if criteria_kms > 0 else '--')
        
        self.edit_years_input.setText(str(int(criteria_years)) if criteria_years > 0 else '')
        self.edit_kms_input.setText(str(int(criteria_kms)) if criteria_kms > 0 else '')
    
    def clear_criteria_details(self):
        """Clear criteria details."""
        self.detail_ba_number.setText("--")
        self.detail_make_type.setText("--")
        self.detail_date_commission.setText("--")
        self.detail_vintage.setText("--")
        self.detail_meterage.setText("--")
        self.detail_status.setText("--")
        self.detail_status.setStatus("normal")
        
        self.criteria_years_label.setText("--")
        self.criteria_kms_label.setText("--")
        
        self.edit_years_input.setText("")
        self.edit_kms_input.setText("")
        self.edit_years_input.setEnabled(False)
        self.edit_kms_input.setEnabled(False)
        
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.create_demand_button.setEnabled(False)
        self.save_criteria_button.setEnabled(False)
        self.discard_button.setEnabled(False)
    
    def save_criteria_changes(self):
        """Save criteria changes from quick edit."""
        selected_row = self.criteria_table.currentRow()
        if selected_row < 0:
            return
        
        criteria_id = self.criteria_table.item(selected_row, 0).data(Qt.UserRole)
        equipment_id = self.criteria_table.item(selected_row, 0).data(Qt.UserRole + 1)
        
        try:
            years_text = self.edit_years_input.text().strip()
            kms_text = self.edit_kms_input.text().strip()
            
            years = int(years_text) if years_text else 0
            kms = int(kms_text) if kms_text else 0
            
            if years == 0 and kms == 0:
                QMessageBox.warning(self, "Validation Error", 
                                  "At least one criteria (Years or KMs) must be specified.")
                return
            
            criteria_model = models.DiscardCriteria(
                discard_criteria_id=criteria_id,
                equipment_id=equipment_id,
                criteria_years=years,
                criteria_kms=kms
            )
            
            result = criteria_model.save()
            
            if result:
                self.load_data()
                
                for row in range(self.criteria_table.rowCount()):
                    item = self.criteria_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == criteria_id:
                        self.criteria_table.selectRow(row)
                        self.criteria_selected(row)
                        break
                
                QMessageBox.information(self, "Success", 
                                      "Discard criteria updated successfully.")
            else:
                QMessageBox.critical(self, "Error", 
                                   "Failed to update discard criteria.")
                
        except ValueError:
            QMessageBox.warning(self, "Validation Error", 
                              "Please enter valid numbers for Years and KMs.")
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                               f"Error updating discard criteria: {str(e)}")
    
    def add_criteria(self):
        """Add new discard criteria."""
        equipment_list = models.Equipment.get_active()
        
        dialog = DiscardCriteriaDialog(equipment_list=equipment_list, parent=self)
        if dialog.exec_():
            criteria_data = dialog.get_criteria_data()
            
            criteria = models.DiscardCriteria(
                equipment_id=criteria_data.get('EquipmentID'),
                criteria_years=criteria_data.get('CriteriaYears'),
                criteria_kms=criteria_data.get('CriteriaKMs')
            )
            
            try:
                criteria.save()
                self.load_data()
                QMessageBox.information(self, "Success", 
                                      "Discard criteria added successfully.")
            except Exception as e:
                QMessageBox.critical(self, "Error", 
                                   f"Error adding discard criteria: {str(e)}")
    
    def edit_criteria(self):
        """Edit selected discard criteria."""
        selected_row = self.criteria_table.currentRow()
        if selected_row < 0:
            return
        
        criteria_id = self.criteria_table.item(selected_row, 0).data(Qt.UserRole)
        
        criteria = models.DiscardCriteria.get_by_id(criteria_id)
        if not criteria:
            return
        
        equipment_list = models.Equipment.get_active()
        
        dialog = DiscardCriteriaDialog(criteria, equipment_list, parent=self)
        if dialog.exec_():
            criteria_data = dialog.get_criteria_data()
            
            criteria_model = models.DiscardCriteria(
                discard_criteria_id=criteria_id,
                equipment_id=criteria_data.get('EquipmentID'),
                criteria_years=criteria_data.get('CriteriaYears'),
                criteria_kms=criteria_data.get('CriteriaKMs')
            )
            
            try:
                criteria_model.save()
                self.load_data()
                
                for row in range(self.criteria_table.rowCount()):
                    item = self.criteria_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == criteria_id:
                        self.criteria_table.selectRow(row)
                        self.criteria_selected(row)
                        break
                
                QMessageBox.information(self, "Success", 
                                      "Discard criteria updated successfully.")
            except Exception as e:
                QMessageBox.critical(self, "Error", 
                                   f"Error updating discard criteria: {str(e)}")
    
    def delete_criteria(self):
        """Delete selected discard criteria."""
        selected_row = self.criteria_table.currentRow()
        if selected_row < 0:
            return
        
        criteria_id = self.criteria_table.item(selected_row, 0).data(Qt.UserRole)
        
        criteria = models.DiscardCriteria.get_by_id(criteria_id)
        if not criteria:
            return
        
        ba_number = criteria.get('ba_number') or '--'
        make_type = criteria.get('make_and_type') or '--'
        confirm = QMessageBox.question(
            self, "Confirm Deletion", 
            f"Are you sure you want to delete discard criteria for:\n\n"
            f"BA Number: {ba_number}\n"
            f"Equipment: {make_type}",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if confirm != QMessageBox.Yes:
            return
        
        try:
            models.DiscardCriteria.delete(criteria_id)
            self.load_data()
            QMessageBox.information(self, "Success", 
                                  "Discard criteria deleted successfully.")
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                               f"Error deleting discard criteria: {str(e)}")
    
    def create_demand(self):
        """Create equipment demand forecast for discarded equipment."""
        selected_row = self.criteria_table.currentRow()
        if selected_row < 0:
            return
        
        equipment_id = self.criteria_table.item(selected_row, 0).data(Qt.UserRole + 1)
        
        equipment = models.Equipment.get_by_id(equipment_id)
        if not equipment:
            QMessageBox.critical(self, "Error", "Equipment not found.")
            return
        
        try:
            current_year = datetime.now().year
            fiscal_year = f"{current_year}-{current_year + 1}"
            
            forecast = models.EquipmentForecast(
                equipment_id=equipment_id,
                fiscal_year=fiscal_year,
                equipment_type=getattr(equipment, 'make_and_type', ''),
                replacement_reason="Discarded - Meeting Discard Criteria",
                quantity_required=1,
                total_requirement=1.0,
                remarks="Equipment no longer in service"
            )
            
            forecast_id = forecast.save()
            
            if forecast_id:
                ba_number = getattr(equipment, 'ba_number', '--')
                make_type = getattr(equipment, 'make_and_type', '--')
                
                result = QMessageBox.question(
                    self, "Demand Created Successfully", 
                    f"Equipment demand forecast has been created for:\n\n"
                    f"BA Number: {ba_number}\n"
                    f"Equipment: {make_type}\n"
                    f"Fiscal Year: {fiscal_year}\n\n"
                    f"Would you like to view the Equipment Forecast tab?",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
                )
                
                if result == QMessageBox.Yes and self.main_window:
                    # Navigate to demand forecast tab
                    for i in range(self.main_window.tab_widget.count()):
                        widget = self.main_window.tab_widget.widget(i)
                        if hasattr(widget, '__class__') and 'DemandForecast' in widget.__class__.__name__:
                            self.main_window.tab_widget.setCurrentIndex(i)
                            if hasattr(widget, 'tab_widget'):
                                for j in range(widget.tab_widget.count()):
                                    if 'Equipment' in widget.tab_widget.tabText(j):
                                        widget.tab_widget.setCurrentIndex(j)
                                        break
                            break
            else:
                QMessageBox.critical(self, "Error", "Failed to create equipment demand forecast.")
                
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                               f"Error creating demand forecast: {str(e)}")
    
    def mark_equipment_discarded(self):
        """Mark selected equipment as discarded."""
        selected_row = self.criteria_table.currentRow()
        if selected_row < 0:
            return

        equipment_id = self.criteria_table.item(selected_row, 0).data(Qt.UserRole + 1)

        # Get equipment details for confirmation
        equipment = models.Equipment.get_by_id(equipment_id)
        if not equipment:
            QMessageBox.critical(self, "Error", "Equipment not found.")
            return

        ba_number = getattr(equipment, 'ba_number', '--')
        make_type = getattr(equipment, 'make_and_type', '--')

        # Confirm discard action
        confirm = QMessageBox.question(
            self, "Confirm Equipment Discard",
            f"Are you sure you want to mark this equipment as DISCARDED?\n\n"
            f"BA Number: {ba_number}\n"
            f"Equipment: {make_type}\n\n"
            f"This action will:\n"
            f"• Remove equipment from overhaul tables\n"
            f"• Mark equipment status as 'discarded'\n"
            f"• Equipment will only appear in discard criteria tab\n\n"
            f"This action cannot be easily undone.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if confirm != QMessageBox.Yes:
            return

        try:
            from datetime import date
            discard_date = date.today().isoformat()
            discard_reason = "Meeting discard criteria - manually discarded"

            # Mark equipment as discarded
            success = models.Equipment.mark_as_discarded(equipment_id, discard_date, discard_reason)

            if success:
                # Refresh the data to reflect changes
                self.load_data()

                # Notify other tabs to refresh their data
                if hasattr(self, 'main_window') and self.main_window:
                    # Refresh overhaul tab to remove discarded equipment
                    for i in range(self.main_window.tab_widget.count()):
                        widget = self.main_window.tab_widget.widget(i)
                        if hasattr(widget, 'load_data') and 'repairs' in widget.__class__.__name__.lower():
                            widget.load_data()
                            break

                QMessageBox.information(
                    self, "Equipment Discarded Successfully",
                    f"Equipment has been marked as discarded:\n\n"
                    f"BA Number: {ba_number}\n"
                    f"Equipment: {make_type}\n\n"
                    f"The equipment will no longer appear in overhaul tables."
                )
            else:
                QMessageBox.critical(self, "Error", "Failed to mark equipment as discarded.")

        except Exception as e:
            QMessageBox.critical(self, "Error",
                               f"Error marking equipment as discarded: {str(e)}")

    def select_discard_criteria(self, criteria_id):
        """Select discard criteria with the given ID."""
        for row in range(self.criteria_table.rowCount()):
            item = self.criteria_table.item(row, 0)
            if item and item.data(Qt.UserRole) == criteria_id:
                self.criteria_table.selectRow(row)
                self.criteria_selected(row)
                break